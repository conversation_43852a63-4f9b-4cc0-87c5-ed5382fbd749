// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAAtk487R6djBc5ACqE7B08-bu48ozhYhc',
    appId: '1:407839569893:web:b50ebba9eda0f4813ae10c',
    messagingSenderId: '407839569893',
    projectId: 'get-clean-43a3c',
    authDomain: 'get-clean-43a3c.firebaseapp.com',
    storageBucket: 'get-clean-43a3c.appspot.com',
    measurementId: 'G-VHCN63WVS5',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCpnYVPzAjfB02RHv-qWXQHOUAoVs3KsMM',
    appId: '1:407839569893:android:9059a041332e1eea3ae10c',
    messagingSenderId: '407839569893',
    projectId: 'get-clean-43a3c',
    storageBucket: 'get-clean-43a3c.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDpAB0jz8OoYkaK95dHEQgn-JZ6-nInego',
    appId: '1:407839569893:ios:e0dff9af79dbc76f3ae10c',
    messagingSenderId: '407839569893',
    projectId: 'get-clean-43a3c',
    storageBucket: 'get-clean-43a3c.appspot.com',
    androidClientId: '407839569893-apjogf9gdvn9tgkr89kk5ht4rmh4qgtg.apps.googleusercontent.com',
    iosClientId: '407839569893-67mcv8utbp6hehr5uoevsflkfbamtkv6.apps.googleusercontent.com',
    iosBundleId: 'com.vishvish.vishvishuserapp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDp1z17FwWDYGzVlznV-yB5NmbT4p0f3VA',
    appId: '1:407839569893:ios:ba58aff6b56d29293ae10c',
    messagingSenderId: '407839569893',
    projectId: 'get-clean-43a3c',
    storageBucket: 'get-clean-43a3c.appspot.com',
    androidClientId: '407839569893-apjogf9gdvn9tgkr89kk5ht4rmh4qgtg.apps.googleusercontent.com',
    iosClientId: '407839569893-nmi6p3ediadqhq48p5s06mjti6mbufs9.apps.googleusercontent.com',
    iosBundleId: 'com.cleanapp.getClean',
  );
}
