// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/widget/custom_rating_bar.dart';

import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/widget/custom_button.dart';

class OfferServiceWidget extends StatelessWidget {
  final onBookPressed,
      imageURL,
      name,
      address,
      onOfferServicePressed,
      providerName,
      providerImage,
      rating;
  const OfferServiceWidget({
    Key? key,
    this.onBookPressed,
    this.onOfferServicePressed,
    this.imageURL,
    this.name,
    this.address,
    this.providerImage,
    this.providerName,
    this.rating,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onOfferServicePressed,
      child: Container(
        margin: const EdgeInsets.all(5),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey[400]!,
              blurRadius: 4,
            ),
          ],
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(15),
              child: Image.network(
                imageURL,
                fit: BoxFit.fill,
                height: 90.h,
                width: 90.w,
                errorBuilder: (context, error, stackTrace) => SizedBox(
                  height: 90.h,
                  width: 90.w,
                ),
              ),
            ),
            SizedBox(width: 10.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text(
                    name,
                    style: middleTextStyle,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Row(
                    children: [
                      const Icon(
                        FontAwesomeIcons.locationPin,
                        size: 17,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        address,
                        style: middleTextStyle,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 10.sp,
                            backgroundImage: NetworkImage(providerImage),
                          ),
                          SizedBox(width: 5.w),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                providerName,
                                style: smallTextStyle,
                              ),
                              CustomRatingBar(
                                iconSize: 10.0.sp,
                                padding: 1.0.sp,
                                initialRating: rating,
                                canChangeRate: false,
                              ),
                            ],
                          ),
                        ],
                      ),
                      CustomButton(
                        label:
                            Get.find<LanguageController>().keys.value.bookNow!,
                        onTap: onBookPressed,
                        height: 25.h,
                        width: 50.w,
                        borderRadius: 5.0,
                        fontSize: 12,
                      ),
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
