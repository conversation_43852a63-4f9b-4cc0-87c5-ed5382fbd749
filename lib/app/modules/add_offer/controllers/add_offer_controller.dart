import 'dart:io';
import 'package:dio/dio.dart' as d;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_offer/provider/add_offer_remote_provider.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import '../../../../global/models/work_zones.dart';

class AddOfferController extends GetxController {
  final homeController = Get.find<HomeController>();
  final provider = AddOfferRemoteProvider();

  final offerImage = File('').obs;
  final choosedWorkZone = WorkZones().obs;

  final isActive = false.obs;

  TextEditingController nameController = TextEditingController();
  TextEditingController priceController = TextEditingController();
  TextEditingController fromDateController = TextEditingController();
  TextEditingController toDateController = TextEditingController();
  TextEditingController averageTimeController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();

  void onOfferImagePressed() async {
    offerImage.value = await pickFile();
    update();
  }

  void onAddressChanged(value) {
    choosedWorkZone.value = value;
    update();
  }

  void onchangeIsActive(value) {
    isActive.value = value;
    update();
  }

  void onTapSave() async {
    showWaitingIndicator();

    final response = await provider.addNewOffer({
      "district_id": choosedWorkZone.value.id,
      "name": nameController.text,
      "description": descriptionController.text,
      "duration": averageTimeController.text,
      "start_date": fromDateController.text,
      "end_date": toDateController.text,
      "image": await d.MultipartFile.fromFile(
        offerImage.value.path,
        filename: 'offerImage.png',
      ),
      "is_active": isActive.value,
      "price": priceController.text,
    });

    hideWaitingIndicator();

    if (response) {
      Get.back();
    }
  }
}
