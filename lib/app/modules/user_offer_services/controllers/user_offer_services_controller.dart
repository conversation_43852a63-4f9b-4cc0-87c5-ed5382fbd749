import 'package:get/get.dart';
import 'package:get_clean/app/modules/user_offer_services/provider/remote_provider.dart';
import 'package:get_clean/global/models/user_booking.dart';

class UserOfferServicesController extends GetxController {
  final pendingOfferOrders = <BookingData>[].obs;
  final waitingOfferOrders = <BookingData>[].obs;
  final unPaidOfferOrders = <BookingData>[].obs;

  final loading = false.obs;

  @override
  void onInit() {
    super.onInit();

    getUserOfferBooking();
  }

  final user = UserOfferServiceRemoteUser();

  void getUserOfferBooking() async {
    loading.value = true;

    //? filter orders by offer service
    final pendingOffers = await user.getPendingOffers();
    final waitingOffers = await user.getWaitingOffers();
    final unPaidOfferOffers = await user.getUnPaidOffers();

    pendingOfferOrders.value = pendingOffers?.bookingData ?? [];

    waitingOfferOrders.value = waitingOffers?.bookingData ?? [];

    unPaidOfferOrders.value = unPaidOfferOffers?.bookingData ?? [];

    loading.value = false;

    update();
  }

  // * Change Tab Index
  final tabIndex = 0.obs;

  void onChangeIndex(int index) {
    tabIndex.value = index;
    update();
  }
}
