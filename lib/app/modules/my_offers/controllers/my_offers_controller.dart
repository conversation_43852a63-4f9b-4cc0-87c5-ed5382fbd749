import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_offers/provider/my_offers_remote_provider.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/models/my_offers_model.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/help_functions/help_functions.dart';
import 'states/my_offers_states.dart';

class MyOffersController extends GetxController {
  final provider = MyOffersRemoteProvider();
  final myOffers = MyOffersModel().obs;
  final myOffersState = MyOffersState().obs;

  @override
  void onInit() {
    super.onInit();
    getMyOffers();
  }

  void getMyOffers() async {
    myOffersState.value = MyOffersLoadingState();
    update();
    myOffersState.value = await provider.getMyOffers();
    update();

    if (myOffersState.value is MyOffersSuccessState) {
      myOffers.value = myOffersState.value.myOffers!;
    }
    update();
  }

  void onAddOfferPressed() async {
    await Get.toNamed(Routes.ADD_OFFER);
    getMyOffers();
  }

  void onEditPressed(OfferData offer) async {
    await Get.toNamed(
      Routes.EDIT_OFFER,
      arguments: {'offer': offer},
    );
    getMyOffers();
  }

  void onDeletePressed(OfferData offer) {
    Get.defaultDialog(
      title: Get.find<LanguageController>().keys.value.deleteOffer!,
      middleText: Get.find<LanguageController>().keys.value.deleteOfferMessage!,
      onConfirm: () async {
        showWaitingIndicator();
        final response = await provider.deleteOffer(offer.id!);
        if (response) {
          myOffers.value.data!.remove(offer);
          update();
        }
        hideWaitingIndicator();
        Get.back();
      },
      textConfirm: Get.find<LanguageController>().keys.value.yes!,
      textCancel: Get.find<LanguageController>().keys.value.no!,
    );
  }

  void onActiveChanged(value, OfferData offer) {
    offer.isActive = value;
    provider.setActivityOfOffer(offer.id!, offer.isActive!);
    update();
  }
}
