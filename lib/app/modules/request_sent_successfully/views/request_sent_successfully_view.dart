import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/widget/custom_button.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/request_sent_successfully_controller.dart';

class RequestSentSuccessfullyView
    extends GetView<RequestSentSuccessfullyController> {
  const RequestSentSuccessfullyView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        padding: const EdgeInsets.all(10),
        width: Get.width,
        height: Get.height,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/images/main_background.png',
            ),
            fit: BoxFit.fill,
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(
                'assets/images/check.png',
                width: 142.w,
                height: 142.h,
                fit: BoxFit.fill,
              ),
              SizedBox(
                height: 10.h,
              ),
              Text(
                Get.find<LanguageController>()
                    .keys
                    .value
                    .requestSentSuccessfully!,
                style: bigTextStyle,
                textAlign: TextAlign.center,
              ),
              SizedBox(
                height: 4.h,
              ),
              Text(
                Get.find<LanguageController>()
                    .keys
                    .value
                    .providerWillContactYou!,
                style: big2TextStyle,
                textAlign: TextAlign.center,
              ),
              SizedBox(
                height: 10.h,
              ),
              if (Get.arguments != null)
                CustomButton(
                  label: Get.find<LanguageController>()
                      .keys
                      .value
                      .gotoOrderDetialsPage!,
                  onTap: () => Get.offNamed(
                    Routes.ORDER_DETAILS,
                    arguments: {'order': Get.arguments['order']!},
                  ),
                  height: 50.h,
                  width: 300.w,
                )
              else
                CustomButton(
                  label: Get.find<LanguageController>().keys.value.done!,
                  onTap: () => Get.offAllNamed(Routes.HOME),
                  height: 50.h,
                  width: 300.w,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
