import 'dart:developer';

import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

import '../../../../global/models/user.dart';
import '../../../../global/models/work_location_model.dart';

class MyProfileRemoteProvider {
  DioHelper helper = DioHelper();

  Future<User?> editProfileData(data, {String? userImage}) async {
    try {
      final response =
          await helper.postData(editProfileURL, data, image: userImage);

      log('DATAAAAAAA ${response}');

      if (response['success'] == true) {
        showSuccessToast(response['message']);

        return User.fromJson(response['data']);
      } else {
        showErrorToast(response['message']);
        return null;
      }
    } catch (e) {
      log(e.toString());
      return null;
    }
  }

  // delete profile
  Future<void> deleteProfile({User? user}) async {
    try {
      final response = await helper.postData(deleteProfileURL, {
        'id': user?.id,
        'type': user?.type ?? 'user',
      });
      log('delete profile response: $response');
    } catch (e) {
      log(e.toString());
    }
  }

  // Work Locations API Methods

  // Get work locations
  Future<WorkLocationResponse?> getWorkLocations() async {
    try {
      final response = await helper.getData(locationsUrl);

      if (response['status'] == true) {
        return WorkLocationResponse.fromJson(response);
      } else {
        showErrorToast(response['message']);
        return null;
      }
    } catch (e) {
      log('Error getting work locations: ${e.toString()}');
      return null;
    }
  }

  // Add work location
  Future<bool> addWorkLocation(WorkLocation location) async {
    try {
      final response = await helper.postData(locationsUrl, {
        'name': location.name,
        'latitude': location.latitude,
        'longitude': location.longitude,
      });

      if (response['status'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log('Error adding work location: ${e.toString()}');
      return false;
    }
  }

  // Edit work location
  Future<bool> editWorkLocation(WorkLocation location) async {
    try {
      final response = await helper.postData(editLocationsUrl, {
        'id': location.id,
        'name': location.name,
        'latitude': location.latitude,
        'longitude': location.longitude,
      });

      if (response['status'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log('Error updating work location: ${e.toString()}');
      return false;
    }
  }

  // Delete work location
  Future<bool> deleteWorkLocation(int id) async {
    try {
      final response = await helper.postData(deleteLocationsUrl, {
        'id': id,
      });

      if (response['status'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log('Error deleting work location: ${e.toString()}');
      return false;
    }
  }
}
