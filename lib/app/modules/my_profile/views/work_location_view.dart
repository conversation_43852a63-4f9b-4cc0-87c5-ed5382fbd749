import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_profile/controllers/my_profile_controller.dart';
import 'package:get_clean/app/modules/my_profile/views/widgets/work_location_form.dart';
import 'package:get_clean/app/modules/provider_page_filtered/views/widgets/map_location_picker.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../../global/models/work_location_model.dart';

class WorkLocationView extends StatefulWidget {
  const WorkLocationView({Key? key}) : super(key: key);

  @override
  State<WorkLocationView> createState() => _WorkLocationViewState();
}

class _WorkLocationViewState extends State<WorkLocationView> {
  final controller = Get.find<MyProfileController>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.fetchWorkLocations();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          Get.find<LanguageController>().keys.value.workLocations ??
              'Work Locations',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: primaryColor,
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Obx(() {
        if (controller.isLoadingWorkLocations.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.workLocations.isEmpty) {
          return Center(
            child: Text(
              Get.find<LanguageController>().keys.value.noWorkLocationsFound ??
                  'No work locations found',
              style: const TextStyle(fontSize: 16),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: controller.workLocations.length,
          itemBuilder: (context, index) {
            final location = controller.workLocations[index];
            return _buildWorkLocationCard(location);
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          controller.clearWorkLocationSelection();
          _showAddLocationBottomSheet();
        },
        backgroundColor: primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildWorkLocationCard(WorkLocation location) {
    return Card(
      color: Colors.white,
      surfaceTintColor: Colors.white,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    location.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, color: primaryColor),
                      onPressed: () {
                        controller.selectWorkLocation(location);
                        _showEditLocationBottomSheet();
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _showDeleteConfirmation(location),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            // SizedBox(
            //   width: double.infinity,
            //   child: ElevatedButton.icon(
            //     onPressed: () {
            //       _viewOnMap(location);
            //     },
            //     icon: const Icon(Icons.map),
            //     label: Text(
            //         Get.find<LanguageController>().keys.value.viewOnMap ??
            //             'View on Map'),
            //     style: ElevatedButton.styleFrom(
            //       backgroundColor: primaryColor,
            //       foregroundColor: Colors.white,
            //       padding: const EdgeInsets.symmetric(vertical: 12),
            //       shape: RoundedRectangleBorder(
            //         borderRadius: BorderRadius.circular(8),
            //       ),
            //     ),
            //   ),
            // ),
            SizedBox(
              height: 150,
              width: double.infinity,
              child: Builder(builder: (context) {
                final latitude = double.tryParse(location.latitude) ?? 0.0;
                final longitude = double.tryParse(location.longitude) ?? 0.0;
                final marker = <Marker>{
                  Marker(
                    markerId: MarkerId(location.id.toString()),
                    position: LatLng(latitude, longitude),
                    infoWindow: InfoWindow(title: location.name),
                  ),
                };

                return ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: MapLocationPicker(
                    selectedMarkers: marker,
                    onSave: (_) {},
                    viewOnly: true,
                    hideControls: true,
                  ),
                );
              }),
            )
          ],
        ),
      ),
    );
  }

  void _showAddLocationBottomSheet() {
    Get.bottomSheet(
      const WorkLocationForm(),
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
    );
  }

  void _showEditLocationBottomSheet() {
    Get.bottomSheet(
      const WorkLocationForm(isEdit: true),
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
    );
  }

  void _viewOnMap(WorkLocation location) {
    final latitude = double.tryParse(location.latitude) ?? 0.0;
    final longitude = double.tryParse(location.longitude) ?? 0.0;

    final marker = <Marker>{
      Marker(
        markerId: MarkerId(location.id.toString()),
        position: LatLng(latitude, longitude),
        infoWindow: InfoWindow(title: location.name),
      ),
    };

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (BuildContext context) => MapLocationPicker(
          selectedMarkers: marker,
          onSave: (_) {},
          viewOnly: true,
        ),
      ),
    );
  }

  void _showDeleteConfirmation(WorkLocation location) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Text(Get.find<LanguageController>().keys.value.deleteLocation ??
            'Delete Location'),
        content: Text(
          Get.find<LanguageController>()
                  .keys
                  .value
                  .deleteLocationConfirmation ??
              'Are you sure you want to delete this location?',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              Get.find<LanguageController>().keys.value.cancel ?? 'Cancel',
              style: const TextStyle(color: Colors.black),
            ),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              if (location.id != null) {
                await controller.deleteWorkLocation(location.id!);
              }
            },
            child: Text(
              Get.find<LanguageController>().keys.value.delete ?? 'Delete',
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
