import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/models/user_booking.dart';
import '../../../routes/app_pages.dart';
import '../../privacy_policy/views/privacy_policy_view.dart';
import '../provider/payment_remote_provider.dart';
import 'states/payment_states.dart';

class PaymentController extends GetxController {
  final BookingData order = Get.arguments['order'];
  final bool payDeposit = Get.arguments['payDeposit'];

  final provider = PaymentRemoteProvider();
  final state = PaymentState().obs;

  final paidTips = 0.0.obs;

  // void addTipAsUser() {
  //   final tipController = TextEditingController();
  //   final key = GlobalKey<FormState>();
  //   Get.dialog(
  //     Scaffold(
  //       backgroundColor: Colors.transparent,
  //       body: Form(
  //         key: key,
  //         child: Center(
  //           child: Container(
  //             padding: const EdgeInsets.all(10),
  //             height: Get.height * 0.3,
  //             width: Get.width * 0.9,
  //             decoration: BoxDecoration(
  //               borderRadius: BorderRadius.circular(10),
  //               color: Colors.white,
  //             ),
  //             child: Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               children: [
  //                 Container(
  //                   alignment: Alignment.centerRight,
  //                   child: TextButton(
  //                     onPressed: Get.back,
  //                     child: const Icon(Icons.clear),
  //                   ),
  //                 ),
  //                 Text(
  //                   Get.find<LanguageController>().keys.value.addTip!,
  //                   style: big2TextStyle,
  //                 ),
  //                 CustomFormField(
  //                   keyboardType: TextInputType.number,
  //                   controller: tipController,
  //                   hint: Get.find<LanguageController>().keys.value.amount!,
  //                   validator: (value) {
  //                     if (value.isEmpty) {
  //                       return 'Please Insert The Tip';
  //                     }
  //                     return null;
  //                   },
  //                 ),
  //                 CustomButton(
  //                   label: Get.find<LanguageController>().keys.value.submit!,
  //                   onTap: () async {
  //                     if (key.currentState!.validate()) {
  //                       Get.back();
  //                       state.value = PaymentLoadingState();
  //                       update();
  //
  //                       state.value = await provider.addTip(
  //                         order.id!.toInt(),
  //                         num.parse(tipController.text),
  //                       );
  //
  //                       if (state.value is PaymentSuccessState) {
  //                         paidTips.value = double.parse(tipController.text);
  //                       }
  //                       update();
  //                     }
  //                   },
  //                   height: 50.h,
  //                   width: 200.w,
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       ),
  //     ),
  //     barrierDismissible: true,
  //   );
  // }

  void confirmPayment() async {
    state.value = PaymentLoadingState();
    update();
    if (payDeposit) {
      state.value = await provider.approveOrderAsUser(order.id!.toInt());
    } else {
      state.value = await provider.payRestOfTotalPrice(order.id!.toInt());
    }

    update();

    log('PaymentLink -> ${state.value.paymentLink}');

    if (state.value.paymentLink != null) {
      // await launchUrl(Uri.parse(state.value.paymentLink!));
      Get.dialog(
        AlertDialog(
          contentPadding: const EdgeInsets.all(0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            Get.find<LanguageController>().keys.value.privacyPolicy!,
            style: bigTextStyle,
          ),
          content: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: Get.height * 0.4,
              minWidth: 200,
              maxWidth: 400,
            ),
            child: PrivacyPolicyView(
              fromPayment: true,
            ),
          ),
          actions: [
            TextButton(
              onPressed: Get.back,
              child: Text(
                Get.find<LanguageController>().keys.value.close!,
                style: regularTextStyle,
              ),
            ),
            TextButton(
              onPressed: () {
                Get.back();
                Get.toNamed(
                  Routes.PAYMENT_WEBVIEW,
                  arguments: {
                    'url': state.value.paymentLink!,
                    'order': order,
                    'tip': paidTips.value,
                    'payDeposit': payDeposit,
                  },
                );
              },
              child: Text(
                Get.find<LanguageController>().keys.value.accept!,
                style: regularTextStyle,
              ),
            ),
          ],
        ),
      );
    }
  }
}
