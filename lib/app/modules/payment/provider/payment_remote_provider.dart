import 'dart:developer';

import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

import '../controllers/states/payment_states.dart';

class PaymentRemoteProvider {
  DioHelper helper = DioHelper();

  Future<PaymentState> addTip(int orderId, num amount) async {
    try {
      final response = await helper.postData(
        addTipURL + orderId.toString(),
        {"amount": amount.toDouble()},
      );

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return PaymentSuccessState(null);
      } else {
        showErrorToast(response['message']);
        return PaymentIdel();
      }
    } catch (e) {
      log(e.toString());
      return PaymentIdel();
    }
  }

  Future<PaymentState> approveOrderAsUser(int orderID) async {
    try {
      final response = await helper.postData(
        confirmBookingURL + orderID.toString(),
        {"": null},
      );

      log('Res ${response.toString()}');

      if (response['success'] == true) {
        return PaymentSuccessState(
          response['data']['payment_link'],
        );
      } else {
        showErrorToast(response['message']);
        return PaymentError();
      }
    } catch (e) {
      log(e.toString());
      return PaymentError();
    }
  }

  Future<PaymentState> payRestOfTotalPrice(int orderID) async {
    try {
      final response = await helper.postData(
        payRestOfTotalPriceURL + orderID.toString(),
        {"": null},
      );

      if (response['success'] == true) {
        return PaymentSuccessState(
          response['data']['payment_link'],
        );
      } else {
        showErrorToast(response['message']);
        return PaymentError();
      }
    } catch (e) {
      log(e.toString());
      return PaymentError();
    }
  }
}
