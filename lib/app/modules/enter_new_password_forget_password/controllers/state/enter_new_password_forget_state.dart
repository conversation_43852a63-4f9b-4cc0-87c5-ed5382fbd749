class EnterNewPasswordForgetState {
  String? successMessage;
  String? errorMessage;
}

class EnterNewPasswordForgetSuccessState extends EnterNewPasswordForgetState {
  EnterNewPasswordForgetSuccessState(String successMessage) {
    this.successMessage = successMessage;
  }
}

class EnterNewPasswordForgetFailedState extends EnterNewPasswordForgetState {
  EnterNewPasswordForgetFailedState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class EnterNewPasswordForgetLoadingState extends EnterNewPasswordForgetState {}
