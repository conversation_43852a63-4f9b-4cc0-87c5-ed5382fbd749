import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/verification_code/controllers/states/verify_code_state.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/loading_widget.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/verification_code_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class VerificationCodeView extends GetView<VerificationCodeController> {
  const VerificationCodeView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<VerificationCodeController>(
      builder: (controller) {
        if (controller.verifyCodeState.value is VerifyCodeLoadingState) {
          return const LoadingWidget();
        }
        return Scaffold(
          body: Container(
            padding: const EdgeInsets.all(10),
            width: Get.width,
            height: Get.height,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/main_background.png',
                ),
                fit: BoxFit.fill,
              ),
            ),
            child: SafeArea(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Text(
                      Get.find<LanguageController>()
                          .keys
                          .value
                          .verificationCode!,
                      style: bigTextStyle,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Text(
                      Get.find<LanguageController>()
                          .keys
                          .value
                          .pleaseEnterVerificationCode!,
                      style: regularTextStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Directionality(
                    textDirection: TextDirection.ltr,
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: SizedBox(
                        width: Get.width * 0.6,
                        child: PinCodeTextField(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          appContext: context,
                          length: 4,
                          onChanged: controller.onChangeOTP,
                          enablePinAutofill: true,
                          pinTheme: PinTheme(
                            shape: PinCodeFieldShape.box,
                            inactiveColor: primaryColor,
                            activeColor: primaryColor,
                            inactiveFillColor: primaryColor,
                            activeFillColor: primaryColor,
                            disabledColor: primaryColor,
                            selectedFillColor: primaryColor,
                            selectedColor: primaryColor,
                            borderRadius: BorderRadius.circular(10),
                            fieldWidth: 50.w,
                            fieldHeight: 50.h,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: TextButton(
                      onPressed: controller.resendCode,
                      child: Text(
                        Get.find<LanguageController>().keys.value.resendCode!,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: CustomButton(
                      label: Get.find<LanguageController>().keys.value.submit!,
                      onTap: controller.onSubmitPressed,
                      height: 50.h,
                      width: 185.w,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
