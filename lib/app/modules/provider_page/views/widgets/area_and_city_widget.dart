import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/service_providers/controllers/service_providers_controller.dart';

import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/widget/custom_drop_down_button.dart';

class AreaAndCityProvidersWidget extends StatelessWidget {
  const AreaAndCityProvidersWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ServiceProvidersController>(
      builder: (controller) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomDropDownButton(
              buttonHeight: 50.0.h,
              buttonWidth: Get.width * 0.4,
              hint: Get.find<LanguageController>().keys.value.chooseArea!,
              value: controller.choosedArea.value.name == null
                  ? null
                  : controller.choosedArea.value,
              label: Get.find<LanguageController>().keys.value.area!,
              onChanged: controller.onChangeArea,
              items: controller.areas.value.data == null
                  ? null
                  : controller.areas.value.data!
                      .map(
                        (data) => DropdownMenuItem(
                          value: data,
                          child: Text(
                            data.name ?? '',
                          ),
                        ),
                      )
                      .toList(),
            ),
            CustomDropDownButton(
              buttonHeight: 50.0.h,
              buttonWidth: Get.width * 0.4,
              hint: Get.find<LanguageController>().keys.value.chooseCity!,
              value: controller.choosedCity.value.name == null
                  ? null
                  : controller.choosedCity.value,
              label: Get.find<LanguageController>().keys.value.city!,
              onChanged: controller.onChangeCity,
              items: controller.cities.value.data == null
                  ? null
                  : controller.cities.value.data!
                      .map(
                        (data) => DropdownMenuItem(
                          value: data,
                          child: Text(
                            data.name ?? '',
                          ),
                        ),
                      )
                      .toList(),
            ),
          ],
        );
      },
    );
  }
}
