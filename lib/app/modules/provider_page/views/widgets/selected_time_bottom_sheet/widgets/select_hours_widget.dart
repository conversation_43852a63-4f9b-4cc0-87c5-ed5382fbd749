import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/app/modules/provider_page/views/widgets/selected_time_bottom_sheet/widgets/multi_widget/main_multi_how_many_hours_widget.dart';
import 'package:get_clean/app/modules/provider_page/views/widgets/selected_time_bottom_sheet/widgets/once_hours_widget.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/models/multi_list_model.dart';
import 'package:get_clean/global/widget/custom_button.dart';

class SelectHoursWidget extends GetView<ProviderPageController> {
  const SelectHoursWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final isHours = controller.serviceFromGlobal.value.pricingOption?.id == 1;

    return Obx(() {
      // * Once Hours ==============================
      if (controller.tabIndex.value == 0) {
        return const OnceHoursWidget();
      }

      if (controller.multiList.isEmpty &&
          controller.avilableTimes.value.data?.bookingDays?.isNotEmpty ==
              true) {
        controller.multiList.value = [
          MultiListModel(
              howManyHours: 2.obs,
              selectedDate: DateTime.now().obs,
              selectedTime: controller.avilableTimes.value.data?.bookingDays
                  ?.firstOrNull?.bookingTimes?.firstOrNull?.obs
              // '01:30 PM'.obs,
              )
        ];
      }

      return Column(
        children: [
          const MainMultiHoursWidget(),

          SizedBox(
            height: 10.h,
          ),

          //! Add Button
          CustomButton(
            label: Get.find<LanguageController>().keys.value.addMore == null ||
                    Get.find<LanguageController>().keys.value.addMore!.isEmpty
                ? 'Add More'
                : Get.find<LanguageController>().keys.value.addMore!,
            onTap: controller.onAddMultiList,
            textColor: primaryColor,
            color: Colors.blueGrey.shade50,
            height: 50,
            width: Get.width * 0.8,
          ),
        ],
      );
    });
  }
}
