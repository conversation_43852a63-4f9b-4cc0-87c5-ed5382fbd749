import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/provider_page_filtered_controller.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../add_new_service/views/widgets/add_service_form_field.dart';

class RequestSheetSofaWidget extends StatelessWidget {
  const RequestSheetSofaWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProviderPageController>(builder: (controller) {
      final pricingList = controller.service.pricingList ?? [];
      // controller.providerServices!
      //     .firstWhereOrNull(
      //         (element) => element.service?.id == controller.service.id)
      //     ?.pricingList
      //     ?.where((element) => element.price != null && element.price != 0)
      //     .toList() ??
      // [];

      log('fkkkkkk ${controller.service.id} asfasff ${controller.service.toJson()}');

      return WillPopScope(
        onWillPop: () async {
          for (var element in selectedTypesSettings.value) {
            element.hours = 0;
          }
          return true;
        },
        child: Container(
          decoration: BoxDecoration(
            // border: Border.all(
            //   color: primaryColor,
            //   width: 0.5,
            // ),
            borderRadius: BorderRadius.circular(25),
            color: Colors.white,
          ),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: primaryColor,
                    width: 0.5,
                  ),
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(25),
                    topLeft: Radius.circular(25),
                  ),
                  color: primaryColor,
                ),
                alignment: Alignment.center,
                child: Text(
                  Get.find<LanguageController>().keys.value.sofas!,
                  style: regularWhiteTextStyle,
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        // TextWithBackground(
                        //   color: primaryColor,
                        //   text: Get.find<LanguageController>().keys.value.sofaType!,
                        // ),
                        for (int i = 0;
                            i <
                                // controller.serviceFromGlobal.value.typesSettings!
                                pricingList.length;
                            i++)
                          AddServiceFormField(
                            keyboardType: TextInputType.text,
                            initialValue:
                                pricingList[i].typeModel?.name.text == '0'
                                    ? ''
                                    : pricingList[i].typeModel?.name.text,
                            active: false,
                          ),
                      ],
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Column(
                      children: [
                        // Row(
                        //   children: [
                        //     Expanded(
                        //       child: TextWithBackground(
                        //         color: primaryColor,
                        //         text: Get.find<LanguageController>().keys.value.hour!,
                        //       ),
                        //     ),
                        //   ],
                        // ),

                        for (int i = 0; i < pricingList.length; i++)
                          Row(
                            children: [
                              Expanded(
                                child: AddServiceFormField(
                                  keyboardType: TextInputType.number,
                                  // initialValue: selectedTypesSettings
                                  //     .value[i].hours
                                  //     ?.toString(), //TODO-API
                                  // controller.serviceFromGlobal.value
                                  //     .typesSettings![i].hours
                                  //     ?.toString(),

                                  onChanged: (value) =>
                                      controller.onChangeHowManyHoursSofa(
                                          int.tryParse(value) ?? 0, i),
                                  active: true,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }
}
