import 'dart:convert';
import 'dart:developer';

import '../../../../global/constants/constants.dart';
import '../../../../global/dio/dio_helper.dart';
import '../../../../global/models/provider_avilable_times.dart';
import '../controllers/states/provider_page_state.dart';

class ProviderPageRemoteProvider {
  DioHelper helper = DioHelper();

  Future<ProviderPageState> getProviderTimes(data) async {
    log(jsonEncode(data));
    try {
      final response = await helper.postData(getProviderTimesURL, data);
      log('asfasfasfsafa ${response}');
      if (response['success'] == true) {
        log('TimeRes: ${response.toString()}');
        return ProviderPageTimesState(
          ProviderAvilableTimes.fromJson(response),
        );
      } else {
        return ProviderPageFailedState(response['message']);
      }
    } catch (e) {
      return ProviderPageFailedState(e.toString());
    }
  }
}
