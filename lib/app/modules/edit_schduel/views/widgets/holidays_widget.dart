import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/edit_schduel/controllers/edit_schduel_controller.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/widget/text_with_background.dart';

class HolidaysWidget extends StatelessWidget {
  const HolidaysWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditSchduelController>(
      builder: (controller) {
        return Column(
          children: [
            // Holidays
            Text(
              Get.find<LanguageController>().keys.value.holidays!,
              style: big2TextStyle,
            ),
            Container(
              padding: const EdgeInsets.all(10),
              margin: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: const Color(0xffF3F3F3),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        TextWithBackground(
                          color: primaryColor,
                          text: Get.find<LanguageController>().keys.value.from!,
                        ),
                        for (int i = 0; i < controller.holidaysLength; i++)
                          TextWithBackground(
                            color: Colors.white,
                            text: controller.holidayFrom(i),
                            textColor: Colors.black,
                          ),
                      ],
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: TextWithBackground(
                                color: primaryColor,
                                text: Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .to!,
                              ),
                            ),
                            const SizedBox(width: 2),
                            InkWell(
                              onTap: () => controller.onAddNewHoliday(context),
                              child: const Icon(
                                FontAwesomeIcons.circlePlus,
                                color: primaryColor,
                                size: 15,
                              ),
                            ),
                          ],
                        ),
                        for (int i = 0; i < controller.holidaysLength; i++)
                          Row(
                            children: [
                              Expanded(
                                child: TextWithBackground(
                                  color: Colors.white,
                                  text: controller.holidayTo(i),
                                  textColor: Colors.black,
                                ),
                              ),
                              const SizedBox(width: 2),
                              InkWell(
                                onTap: () => controller.deleteHoliday(i),
                                child: const Icon(
                                  FontAwesomeIcons.trash,
                                  color: primaryColor,
                                  size: 15,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
