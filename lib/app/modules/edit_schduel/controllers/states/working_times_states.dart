import 'package:get_clean/global/models/working_times_model.dart';

class WorkingTimesState {
  WorkingTimesModel? workingTimesModel;
  String? errorMessage;
}

class WorkingTimesSuccessState extends WorkingTimesState {
  WorkingTimesSuccessState(WorkingTimesModel workingTimesModel) {
    this.workingTimesModel = workingTimesModel;
  }
}

class WorkingTimesFailedState extends WorkingTimesState {
  WorkingTimesFailedState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class WorkingTimesLoadingState extends WorkingTimesState {}
