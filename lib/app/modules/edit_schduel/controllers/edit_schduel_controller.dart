// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/edit_schduel/controllers/states/working_times_states.dart';
import 'package:get_clean/app/modules/edit_schduel/provider/remote_provider.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

import '../../../../global/controllers/language_controller.dart';
import '../../../../global/models/working_times_model.dart';

class EditSchduelController extends GetxController {
  final EditSchduelRemoteProvider provider = EditSchduelRemoteProvider();

  final state = WorkingTimesState().obs;
  final workingTimesModel = WorkingTimesModel().obs;

  final selectedDays = <int>[0, 0, 0, 0, 0, 0, 0].obs;

  final weekDays = <String>[
    Get.find<LanguageController>().keys.value.sun!,
    Get.find<LanguageController>().keys.value.mon!,
    Get.find<LanguageController>().keys.value.tue!,
    Get.find<LanguageController>().keys.value.wed!,
    Get.find<LanguageController>().keys.value.thu!,
    Get.find<LanguageController>().keys.value.fri!,
    Get.find<LanguageController>().keys.value.sat!,
  ].obs;

  final weekDaysToSend = [
    "sunday",
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
  ];

  final weekDaysToSendChoosed = [].obs;

  final fromTime =
      '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()} : ${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()} ${DateTime.now().hour > 12 ? 'PM' : 'AM'}'
          .obs;

  final fromTimeToSend =
      '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()}:${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()}'
          .obs;

  final toTime =
      '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()} : ${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()} ${DateTime.now().hour > 12 ? 'PM' : 'AM'}'
          .obs;

  final toTimeToSend =
      '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()}:${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()}'
          .obs;

  final fromDateHoliday = DateTime.now().toString().obs;
  final toDateHoliday = DateTime.now().toString().obs;

  String dayName(i) {
    return workingTimesModel.value.data!.workingTimes![i].day!;
  }

  String startAt(i) {
    return workingTimesModel.value.data!.workingTimes![i].startsAt!;
  }

  String endAt(i) {
    return workingTimesModel.value.data!.workingTimes![i].endsAt!;
  }

  int get workingTimesLength =>
      workingTimesModel.value.data!.workingTimes!.length;

  int get holidaysLength => workingTimesModel.value.data!.holidays!.length;

  String holidayFrom(i) {
    return workingTimesModel.value.data!.holidays![i].startsAt!;
  }

  String holidayTo(i) {
    return workingTimesModel.value.data!.holidays![i].endsAt!;
  }

  @override
  void dispose() {
    selectedDays.value = <int>[0, 0, 0, 0, 0, 0, 0];

    weekDays.value = <String>[
      Get.find<LanguageController>().keys.value.sun!,
      Get.find<LanguageController>().keys.value.mon!,
      Get.find<LanguageController>().keys.value.tue!,
      Get.find<LanguageController>().keys.value.wed!,
      Get.find<LanguageController>().keys.value.thu!,
      Get.find<LanguageController>().keys.value.fri!,
      Get.find<LanguageController>().keys.value.sat!,
    ];

    weekDaysToSendChoosed.value = [];

    fromTime.value =
        '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()} : ${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()} ${DateTime.now().hour > 12 ? 'PM' : 'AM'}';

    fromTimeToSend.value =
        '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()}:${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()}';

    toTime.value =
        '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()} : ${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()} ${DateTime.now().hour > 12 ? 'PM' : 'AM'}';

    toTimeToSend.value =
        '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()}:${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()}';

    fromDateHoliday.value = DateTime.now().toString();
    toDateHoliday.value = DateTime.now().toString();
    super.dispose();
  }

  @override
  void onInit() async {
    state.value = WorkingTimesLoadingState();
    update();

    getWorkingTimes();

    super.onInit();
  }

  void getWorkingTimes() async {
    state.value = await provider.getWorkingTimes();
    update();

    if (state.value is WorkingTimesSuccessState) {
      workingTimesModel.value = state.value.workingTimesModel!;
    }
  }

  void changeSelectedDay(int index) {
    if (selectedDays[index] == 0) {
      selectedDays[index] = 1;
      weekDaysToSendChoosed.add(weekDaysToSend[index]);
    } else {
      selectedDays[index] = 0;
      weekDaysToSendChoosed.remove(weekDaysToSend[index]);
    }

    update();
  }

  void chooseFromTime(BuildContext context) async {
    final time =
        await showTimePicker(context: context, initialTime: TimeOfDay.now());
    fromTime.value =
        '${time!.hour.toString().length == 1 ? '0${time.hour}' : time.hour.toString()} : ${time.minute.toString().length == 1 ? '0${time.minute}' : time.minute.toString()} ${time.hour > 12 ? 'PM' : 'AM'}';
    fromTimeToSend.value =
        '${time.hour.toString().length == 1 ? '0${time.hour}' : time.hour.toString()}:${time.minute.toString().length == 1 ? '0${time.minute}' : time.minute.toString()}';
    update();
  }

  void chooseToTime(BuildContext context) async {
    final time =
        await showTimePicker(context: context, initialTime: TimeOfDay.now());
    toTime.value =
        '${time!.hour.toString().length == 1 ? '0${time.hour}' : time.hour.toString()} : ${time.minute.toString().length == 1 ? '0${time.minute}' : time.minute.toString()} ${time.hour > 12 ? 'PM' : 'AM'}';

    toTimeToSend.value =
        '${time.hour.toString().length == 1 ? '0${time.hour}' : time.hour.toString()}:${time.minute.toString().length == 1 ? '0${time.minute}' : time.minute.toString()}';
    update();
  }

  Future<void> chooseFromDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(3000),
      helpText: Get.find<LanguageController>().keys.value.fromDateHoliday!,
      confirmText: Get.find<LanguageController>().keys.value.next!,
      cancelText: Get.find<LanguageController>().keys.value.cancel!,
    );
    fromDateHoliday.value =
        '${date!.year}-${date.month.toString().length == 1 ? '0${date.month}' : date.month}-${date.day.toString().length == 1 ? '0${date.day}' : date.day}';
    update();
    return;
  }

  Future<void> chooseToDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(3000),
      helpText: Get.find<LanguageController>().keys.value.toDateHoliday!,
      confirmText: Get.find<LanguageController>().keys.value.ok!,
      cancelText: Get.find<LanguageController>().keys.value.cancel!,
    );
    toDateHoliday.value =
        '${date!.year}-${date.month.toString().length == 1 ? '0${date.month}' : date.month}-${date.day.toString().length == 1 ? '0${date.day}' : date.day}';
    update();
    return;
  }

  void onAddNewHoliday(BuildContext context) async {
    await chooseFromDate(context);
    await chooseToDate(context);
    final response = await provider.setHoliday({
      "starts_at": fromDateHoliday.value,
      "ends_at": toDateHoliday.value,
    });

    if (response) {
      getWorkingTimes();
    }

    update();
  }

  void addWorkingTimes() async {
    if (weekDaysToSendChoosed.isEmpty) {
      showErrorToast(
          Get.find<LanguageController>().keys.value.pleaseChooseDaysFirst!);
      return;
    }
    Map<String, String> data = {};

    // for (var i = 0; i < weekDaysToSendChoosed.length; i++) {
    //   data['days'] = '${data['days'] ?? ''}${weekDaysToSendChoosed[i]},';
    // }

    // replace the last comma with nothind
    // data['days'] = data['days'].toString().replaceRange(
    //       data['days'].toString().length - 1,
    //       data['days'].toString().length,
    //       '',
    //     );

    // data['days[]'] = ;
    for (int i = 0; i < weekDaysToSendChoosed.length; i++) {
      data['days[$i]'] = weekDaysToSendChoosed[i];
    }

    // set start and end time
    // data['start_at'] = fromTimeToSend.value;
    // data['end_at'] = toTimeToSend.value;

    for (int i = 0; i < weekDaysToSendChoosed.length; i++) {
      data['start_at[$i]'] = fromTimeToSend.value;
      data['end_at[$i]'] = toTimeToSend.value;
    }

    // send working times
    final response = await provider.setWorkingTimes(data);

    if (response) {
      // get working times after everything is done
      getWorkingTimes();

      // reset all variables
      weekDaysToSendChoosed.value = [];
      selectedDays.value = <int>[0, 0, 0, 0, 0, 0, 0];

      fromTime.value =
          '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()} : ${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()} ${DateTime.now().hour > 12 ? 'PM' : 'AM'}';

      fromTimeToSend.value =
          '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()}:${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()}';

      toTime.value =
          '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()} : ${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()} ${DateTime.now().hour > 12 ? 'PM' : 'AM'}';

      toTimeToSend.value =
          '${DateTime.now().hour.toString().length == 1 ? '0${DateTime.now().hour}' : DateTime.now().hour.toString()}:${DateTime.now().minute.toString().length == 1 ? '0${DateTime.now().minute}' : DateTime.now().minute.toString()}';
    }
  }

  void deleteWorkingTime(int index) async {
    Get.defaultDialog(
      onConfirm: () async {
        final response = await provider.deleteWorkingTime(
          workingTimesModel.value.data!.workingTimes![index].id.toString(),
        );

        if (response == true) {
          getWorkingTimes();
        }
        update();
        Get.back();
      },
      textConfirm: Get.find<LanguageController>().keys.value.yes!,
      textCancel: Get.find<LanguageController>().keys.value.no!,
      title: Get.find<LanguageController>().keys.value.deleteWorkingTime!,
      middleText:
          Get.find<LanguageController>().keys.value.deleteWorkingTimeMessage!,
    );
  }

  void deleteHoliday(int index) async {
    Get.defaultDialog(
      onConfirm: () async {
        final response = await provider.deleteHoliday(
          workingTimesModel.value.data!.holidays![index].id.toString(),
        );

        if (response == true) {
          getWorkingTimes();
        }
        update();
        Get.back();
      },
      textConfirm: Get.find<LanguageController>().keys.value.yes!,
      textCancel: Get.find<LanguageController>().keys.value.no!,
      title: Get.find<LanguageController>().keys.value.deleteHoliday!,
      middleText:
          Get.find<LanguageController>().keys.value.deleteHolidayMessage!,
    );
  }
}
