import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/widget/custom_button.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/consulation_request_controller.dart';

class ConsulationRequestView extends GetView<ConsulationRequestController> {
  const ConsulationRequestView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: Get.width,
        height: Get.height,
        padding: const EdgeInsets.all(10),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/images/main_background.png',
            ),
            fit: BoxFit.fill,
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                    onPressed: Get.back,
                    icon: const Icon(
                      CupertinoIcons.back,
                      size: 30,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      Get.find<LanguageController>()
                          .keys
                          .value
                          .colsultationRequest!,
                      style: bigTextStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Text(
                        Get.find<LanguageController>()
                            .keys
                            .value
                            .askForYourAdvice!,
                        style: big2TextStyle,
                      ),
                    ),
                    const SizedBox(height: 20),
                    CustomButton(
                      label: Get.find<LanguageController>()
                          .keys
                          .value
                          .financialConsulting!,
                      onTap: controller.onFinacialConsultigPressed,
                      height: 42.0.h,
                      width: 357.w,
                    ),
                    CustomButton(
                      label: Get.find<LanguageController>()
                          .keys
                          .value
                          .legalAdvice!,
                      onTap: controller.onLegalAdvicePressed,
                      height: 42.0.h,
                      width: 357.w,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
