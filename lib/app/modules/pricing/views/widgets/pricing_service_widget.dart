// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';

class PricingServiceWidget extends StatelessWidget {
  final name, onEditPressed, onDeletePressed;
  const PricingServiceWidget({
    super.key,
    this.name,
    this.onDeletePressed,
    this.onEditPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      margin: const EdgeInsets.symmetric(vertical: 5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(14.sp),
        border: Border.all(
          color: primaryColor,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              name,
              style: regularTextStyle,
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: onEditPressed,
                icon: const Icon(
                  FontAwesomeIcons.penToSquare,
                  size: 15,
                ),
              ),
              IconButton(
                onPressed: onDeletePressed,
                icon: const Icon(
                  FontAwesomeIcons.trash,
                  size: 15,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
