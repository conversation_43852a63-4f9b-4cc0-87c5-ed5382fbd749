import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/pricing/controllers/states/pricing_states.dart';
import 'package:get_clean/app/modules/pricing/views/widgets/pricing_service_widget.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/pricing_controller.dart';

class PricingView extends GetView<PricingController> {
  const PricingView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<PricingController>(builder: (controller) {
      return Scaffold(
        body: Container(
          width: Get.width,
          height: Get.height,
          padding: const EdgeInsets.all(10),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                'assets/images/main_background.png',
              ),
              fit: BoxFit.fill,
            ),
          ),
          child: SafeArea(
            child: ListView(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    IconButton(
                      onPressed: Get.back,
                      icon: const Icon(
                        CupertinoIcons.back,
                        size: 30,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        Get.find<LanguageController>().keys.value.pricing!,
                        style: bigTextStyle,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Builder(builder: (context) {
                  if (controller.pricingState.value is PricingLoadingState) {
                    return const Center(child: LoadingWidget());
                  }
                  if (controller.pricingState.value is PricingFailedState) {
                    return Expanded(
                      child: Center(
                        child: Text(
                          controller.pricingState.value.errorMessage!,
                          style: bigTextStyle,
                        ),
                      ),
                    );
                  }
                  return SingleChildScrollView(
                    child: Column(
                      children: [
                        Text(
                          Get.find<LanguageController>()
                              .keys
                              .value
                              .addYourPriceForEachCategory!,
                          style: bigTextStyle,
                        ),
                        ...controller.myServices.value.data!
                            .map((e) => PricingServiceWidget(
                                  name: e.service?.name ?? ' ',
                                  onDeletePressed: () => controller
                                      .onDeleteServicePressed(e!.service!.id!),
                                  onEditPressed: () =>
                                      controller.onEditServicePressed(e),
                                ))
                            .toList(),

                        //             // all list but with the same values
                        //                         ...Get.find<GlobalValuesController>()
                        //                             .allServices
                        //                             .value
                        //                             .data!
                        //                             .map((e) => PricingServiceWidget(
                        //                                   name: e.name!,
                        //                                   onDeletePressed: () =>
                        //                                       controller.onDeleteServicePressed(e.id!),
                        //                                   onEditPressed: () => controller
                        //                                       .onEditServicePressed(MyServiceData(
                        //                                     service: Service(
                        //                                       id: e.id,
                        //                                       name: e.name,
                        //                                       pricingOption: PricingOption(
                        //                                         id: e.pricingOption!.id,
                        //                                         name: e.pricingOption!.name,
                        //                                         hasTypes: e.pricingOption!.hasTypes,
                        //                                         // optionTypes: e.pricingOption!.optionTypes,
                        //                                       ),
                        //                                     ),
                        //                                     pricingList: [],
                        //                                     materialPrice: 0,
                        //                                     withTax: false,
                        //                                     minHours: '0',
                        //                                     deliver: false,
                        //                                   )),
                        //                                 ))
                        //                             .toList(),
                        Center(
                          child: CustomButton(
                            label:
                                Get.find<LanguageController>().keys.value.news!,
                            onTap: controller.onAddNewPressed,
                            height: 47.h,
                            width: 300.w,
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
      );
    });
  }
}
