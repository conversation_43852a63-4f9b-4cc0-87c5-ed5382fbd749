import 'dart:developer';

import 'package:get_clean/app/modules/notifications/controllers/states/notifications_states.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/notifications_model.dart';

class NotificationsRemoteProvider {
  DioHelper helper = DioHelper();

  Future<NotificationState> getAllNotifications() async {
    try {
      final response = await helper.getData(allNotificationsURL);
      if (response['success'] == true) {
        return NotificationsSuccess(NotificationsModel.fromJson(response));
      } else {
        showSuccessToast(response['message']);
        return NotificationsFailed(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return NotificationsFailed(e.toString());
    }
  }

  Future<NotificationState> markNotificationAsReaded(String id) async {
    try {
      final response = await helper.postData(
        markNotificationAsReadedURL,
        {'notification_id': id},
      );
      if (response['success'] == true) {
        return NotificationReadedSuccess();
      } else {
        return NotificationsFailed(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return NotificationsFailed(e.toString());
    }
  }
}
