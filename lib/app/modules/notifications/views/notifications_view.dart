import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/notifications/controllers/states/notifications_states.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

import '../../../../global/controllers/language_controller.dart';
import '../controllers/notifications_controller.dart';

class NotificationsView extends GetView<NotificationsController> {
  const NotificationsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    controller.onInit();
    return GetBuilder<NotificationsController>(
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title:
                Text(Get.find<LanguageController>().keys.value.notifications!),
            centerTitle: true,
          ),
          body: Container(
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/main_background_bottom.png',
                ),
                fit: BoxFit.fill,
              ),
            ),
            child: Builder(
              builder: (context) {
                if (controller.state.value is NotificationsLoading) {
                  return const LoadingWidget();
                }
                return ListView.builder(
                  itemBuilder: (context, index) {
                    return InkWell(
                      onTap: () => controller.markNotificationAsReaded(
                        controller.notifications.value.data![index],
                      ),
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        margin: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          color: controller.notifications.value.data![index]
                                      .isRead ==
                                  true
                              ? Colors.white.withOpacity(0.2)
                              : primaryColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          children: [
                            Column(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  width: 277.w,
                                  child: Text(
                                    controller.notifications.value.data![index]
                                            .title ??
                                        '',
                                    style: middleTextStyle,
                                    overflow: TextOverflow.visible,
                                  ),
                                ),
                                if (controller.notifications.value.data![index]
                                        .data?.body !=
                                    null)
                                  SizedBox(
                                    width: 277.w,
                                    child: Text(
                                      controller.notifications.value
                                              .data![index].data?.body ??
                                          '',
                                      style: middleTextStyle,
                                      overflow: TextOverflow.visible,
                                    ),
                                  ),
                                Text(
                                  controller.notifications.value.data![index]
                                          .sentAt ??
                                      '',
                                  style: smallTextStyle,
                                  overflow: TextOverflow.visible,
                                ),
                                SizedBox(
                                  width: 277.w,
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        controller.notifications.value
                                            .data![index].data!.type!,
                                        style: middleGreyTextStyle,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                  itemCount: controller.notifications.value.data?.length ?? 0,
                );
              },
            ),
          ),
        );
      },
    );
  }
}
