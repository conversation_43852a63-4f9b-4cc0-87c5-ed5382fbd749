import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/notifications/controllers/states/notifications_states.dart';
import 'package:get_clean/app/modules/notifications/provider/notifications_remote_provider.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/notifications_model.dart';
import 'package:intl/intl.dart';

class NotificationsController extends GetxController {
  final provider = NotificationsRemoteProvider();
  final state = NotificationState().obs;
  final notifications = NotificationsModel().obs;

  final dateFormat = DateFormat.yMMMEd();

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      getAllNotifications();
    });
  }

  void getAllNotifications() async {
    state.value = NotificationsLoading();
    update();

    state.value = await provider.getAllNotifications();

    if (state.value is NotificationsSuccess) {
      notifications.value = state.value.notifications!;
    }

    // * Sort
    notifications.value.data!.sort((a, b) {
      return b.sentAt?.compareTo(a.sentAt!) ?? 0;
    });

    if (state.value is NotificationsFailed) {}

    update();
  }

  void markNotificationAsReaded(NotificationData notificationData) async {
    notificationData.isRead = true;

    if (notificationData.data!.type == 'order' &&
        notificationData.data!.details != null) {
      Get.toNamed(
        Routes.ORDER_DETAILS,
        arguments: {
          'order': notificationData.data!.details?.toJson(),
        },
      );
    }

    state.value = await provider.markNotificationAsReaded(notificationData.id!);
    update();

    if (state.value is NotificationsFailed) {
      showErrorToast(state.value.errorMessage!);
    }
  }
}
