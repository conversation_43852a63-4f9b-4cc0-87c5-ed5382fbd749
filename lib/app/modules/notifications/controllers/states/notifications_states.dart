import 'package:get_clean/global/models/notifications_model.dart';

class NotificationState {
  String? errorMessage;
  NotificationsModel? notifications;
}

class NotificationsSuccess extends NotificationState {
  NotificationsSuccess(NotificationsModel notifications) {
    this.notifications = notifications;
  }
}

class NotificationReadedSuccess extends NotificationState {}

class NotificationsFailed extends NotificationState {
  NotificationsFailed(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class NotificationsLoading extends NotificationState {}
