import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_booking_details/views/widgets/item_widget.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/custom_button.dart';

import '../../../../global/controllers/language_controller.dart';
import '../controllers/my_booking_details_controller.dart';

class MyBookingDetailsView extends GetView<MyBookingDetailsController> {
  const MyBookingDetailsView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              'Monday, 22Th October 2022',
              style: big2WhiteTextStyle,
            ),
            Text(
              '10:00 Am - 12:00 Pm',
              style: regularWhiteTextStyle,
            ),
          ],
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              margin: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: primaryColor, width: 0.5.w),
              ),
              child: Column(
                children: [
                  ItemWidget(
                    title:
                        Get.find<LanguageController>().keys.value.serviceType!,
                    description: 'Cleaning House',
                  ),
                  ItemWidget(
                    title: Get.find<LanguageController>().keys.value.frequency!,
                    description: 'Weekly Booking',
                  ),
                  ItemWidget(
                    title:
                        Get.find<LanguageController>().keys.value.providerName!,
                    description: 'Mohamed Ali',
                  ),
                  ItemWidget(
                    title: Get.find<LanguageController>().keys.value.duration!,
                    description: '3, Hour, Weekly',
                  ),
                  ItemWidget(
                    title: Get.find<LanguageController>().keys.value.address!,
                    description: '35 Alkods',
                  ),
                  ItemWidget(
                    title: Get.find<LanguageController>()
                        .keys
                        .value
                        .cleaningMaterial!,
                    description: 'Yes',
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(10),
              margin: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: primaryColor, width: 0.5.w),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    Get.find<LanguageController>().keys.value.paymentDetails!,
                    style: bigTextStyle,
                  ),
                  const Divider(color: primaryColor),
                  ItemWidget(
                    title:
                        Get.find<LanguageController>().keys.value.finalPrice!,
                    description: '400 ILS',
                    showThird: true,
                  ),
                  ItemWidget(
                    title: Get.find<LanguageController>().keys.value.deposit20!,
                    description: '80 ILS',
                    showThird: true,
                    thirdTitle: 'Done',
                  ),
                  ItemWidget(
                    title: Get.find<LanguageController>()
                        .keys
                        .value
                        .cleaningMaterial!,
                    description: '20 ILS',
                    showThird: true,
                    thirdTitle: 'Done',
                  ),
                ],
              ),
            ),
            Container(
              margin: const EdgeInsets.all(10),
              padding: const EdgeInsets.all(10),
              child: Row(
                children: [
                  Text(
                    Get.find<LanguageController>().keys.value.status!,
                    style: regularTextStyle,
                  ),
                  Text(
                    Get.find<LanguageController>().keys.value.accepted!,
                    style: const TextStyle(color: Colors.green),
                  ),
                ],
              ),
            ),
            CustomButton(
              label: Get.find<LanguageController>().keys.value.pay!,
              onTap: () => Get.toNamed(Routes.PAYMENT),
              height: 42.h,
              width: 294.w,
            ),
            CustomButton(
              label: Get.find<LanguageController>()
                  .keys
                  .value
                  .cancelAllAppointments!,
              onTap: () {},
              height: 42.h,
              width: 294.w,
            ),
          ],
        ),
      ),
    );
  }
}
