import '../../../../../global/models/user_booking.dart';

class OrderDetailsState {
  String? errorMessage;
  String? paymentLink;
  BookingData? bookingData;
}

class OrderDetailsLoading extends OrderDetailsState {}

class OrderDetailsSuccess extends OrderDetailsState {
  OrderDetailsSuccess({
    String? paymentLink,
    BookingData? bookingData,
  }) {
    this.paymentLink = paymentLink;
    this.bookingData = bookingData;
  }
}

class OrderDetailsFailed extends OrderDetailsState {
  OrderDetailsFailed(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}
