import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/app/modules/order_details/controllers/state/order_details_states.dart';
import 'package:get_clean/app/modules/order_details/provider/order_details_remote_provider.dart';
import 'package:get_clean/app/modules/order_details/views/widgets/edit_order_clothes_widget.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/user_booking.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';
import 'package:get_clean/global/widget/loading_widget.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/custom_button.dart';
import '../../../routes/app_pages.dart';
import '../../add_new_service/controllers/add_new_service_controller.dart';

class OrderDetailsController extends GetxController {
  BookingData bookingData =
      BookingData.fromJson(Get.arguments != null ? Get.arguments['order'] : {});

  final user = Get.find<HomeController>().user;

  final provider = OrderDetailsRemoteProvider();
  final state = OrderDetailsState().obs;

  bool get isOfferOrder =>
      isOffer(bookingData.orderData?.bookingData?.pricingOption?.id);
  // Get.arguments != null ? (Get.arguments['isOffer'] ?? false) : false;

  bool get isWaitingOffer =>
      Get.arguments != null ? Get.arguments['isWaitingOffer'] : false;

  // bookingData.orderData?.isOffer ?? false;

  final howManyCloth = <int, int>{}.obs;

  void onChangedHowManyCloth(int value, int id) {
    howManyCloth[id] = value;

    update();
  }

  // final services = useState(controller.bookingData.orderData!.types
  //         //         ?.where(
  //         //             (element) => element.amount != null && element.amount != 0)
  //         //         .toList() ??
  //         //     []);

  final services = <(int, String?)>[].obs;

  onAddService({
    required int id,
    required String? name,
  }) {
    // log('afasff22 ${type.toJson()}');
    services.add((id, name));
    update();
  }

  @override
  void onInit() {
    super.onInit();

    log(bookingData.id.toString());

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (isUser() && bookingData.hasPendingPricingRequest!) {
        showExtraTimeDialog();
      }
    });
  }

  bool isUser() {
    return bookingData.user!.id! == user.value.id!;
  }

  bool isProvider() {
    if (bookingData.user!.id! == user.value.id!) {
      return false;
    }
    if (bookingData.provider == null) {
      return true;
    }

    return bookingData.provider?.id == user.value.provider?.userId;
  }

  void cancelOrderAsUser(
    int orderID, {
    bool isOrderChange = false,
  }) async {
    state.value = OrderDetailsLoading();
    update();

    state.value =
        await provider.cancelOrderAsUser(orderID, isOrderChange: isOrderChange);
    update();

    if (state.value is OrderDetailsSuccess) {
      Get.toNamed(Routes.ORDER_CANCELED);
    }

    if (state.value is OrderDetailsFailed) {
      showErrorToast(state.value.errorMessage!);
    }
  }

  void cancelOrderAsProvider(int orderID) async {
    state.value = OrderDetailsLoading();
    update();

    state.value = await provider.cancelOrderAsProvider(orderID);
    update();

    if (state.value is OrderDetailsSuccess) {
      Get.toNamed(Routes.ORDER_CANCELED);
    }

    if (state.value is OrderDetailsFailed) {
      showErrorToast(state.value.errorMessage!);
    }
  }

  // void approveOrderAsProviderAndSubmitOffer() async {
  //   final priceController = TextEditingController();
  //   final key = GlobalKey<FormState>();
  //   Get.dialog(
  //     Scaffold(
  //       backgroundColor: Colors.transparent,
  //       body: Form(
  //         key: key,
  //         child: Center(
  //           child: Container(
  //             padding: const EdgeInsets.all(10),
  //             height: Get.height * 0.3,
  //             width: Get.width * 0.9,
  //             decoration: BoxDecoration(
  //               borderRadius: BorderRadius.circular(10),
  //               color: Colors.white,
  //             ),
  //             child: Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               children: [
  //                 Container(
  //                   alignment: Alignment.centerRight,
  //                   child: TextButton(
  //                     onPressed: () => Get.back(),
  //                     child: const Icon(Icons.clear),
  //                   ),
  //                 ),
  //                 Text(
  //                   Get.find<LanguageController>().keys.value.submitNewOffer!,
  //                   style: big2TextStyle,
  //                 ),
  //                 CustomFormField(
  //                   controller: priceController,
  //                   hint: Get.find<LanguageController>().keys.value.price!,
  //                   validator: (value) {
  //                     if (value.isEmpty) {
  //                       return 'Please Insert Price';
  //                     }
  //                     return null;
  //                   },
  //                 ),
  //                 CustomButton(
  //                   label: Get.find<LanguageController>().keys.value.submit!,
  //                   onTap: () async {
  //                     if (key.currentState!.validate()) {
  //                       state.value = OrderDetailsLoading();
  //                       update();

  //                       state.value = await provider.submitNewOffer(
  //                         bookingData.id!.toInt(),
  //                         double.parse(priceController.text),
  //                       );
  //                       update();

  //                       if (state.value is OrderDetailsSuccess) {
  //                         Get.back();
  //                       }
  //                     }
  //                   },
  //                   height: 50.h,
  //                   width: 200.w,
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       ),
  //     ),
  //     barrierDismissible: true,
  //   );
  // }

  final bookedInDate = TextEditingController().obs;

  get isClothes =>
      bookingData.orderData?.bookingData?.name?.toLowerCase() == "clothes";

  void approveOrderAsProvider({
    bool canApproveClothAsOrder = true,
  }) async {
    state.value = OrderDetailsLoading();
    update();

    if (isOfferOrder) {
      state.value = await provider.confirmOfferAsProvider(
          bookingData.id!.toInt(),
          bookedInDate: bookedInDate.value.text);
    } else if (isClothes && !canApproveClothAsOrder) {
      log("Cloth Order");
      state.value = await provider.approveClothOrderAsProvider(
        bookingData.id!.toInt(),
      );
    } else {
      state.value = await provider.approveOrderAsProvider(
        bookingData.id!.toInt(),
      );
    }

    update();

    if (state.value is OrderDetailsSuccess) {
      Get.toNamed(Routes.ORDER_APPROVED);
    }

    if (state.value is OrderDetailsFailed) {
      showErrorToast(state.value.errorMessage!);
    }
  }

  //clothFinished
  void clothFinished() async {
    state.value = OrderDetailsLoading();
    update();

    state.value = await provider.clothFinished(
      bookingData.id!.toInt(),
    );

    update();

    if (state.value is OrderDetailsSuccess) {
      Get.toNamed(Routes.ORDER_APPROVED);
    }

    if (state.value is OrderDetailsFailed) {
      showErrorToast(state.value.errorMessage!);
    }
  }

  // fixit
  //? new tab -> waiting -> waiting_drop_dawn
  //? new tab -> received -> received
  //? new tab -> waiting for approval -> waiting_approval (only user)
  //? new tab (deliver)

  //? Make navigation to home after provider edit the order

  //? approve order as user
  void approveOrderAsUser() async {
    state.value = OrderDetailsLoading();

    update();

    state.value = await provider.approveOrderAsUser(bookingData.id!.toInt());

    update();

    if (state.value is OrderDetailsSuccess) {
      // go to home
      Get.toNamed(Routes.HOME);
    }

    if (state.value is OrderDetailsFailed) {
      showErrorToast(state.value.errorMessage!);
    }
  }

  void confirmClothOrderAsProvider() async {
    state.value = OrderDetailsLoading();
    update();

    state.value =
        await provider.confirmClothOrderAsProvider(bookingData.id!.toInt());

    update();

    if (state.value is OrderDetailsSuccess) {
      Get.toNamed(Routes.ORDER_APPROVED);
    }

    if (state.value is OrderDetailsFailed) {
      showErrorToast(state.value.errorMessage!);
    }
  }

  //! Approve Offer as Provider
  void approveOfferAsProvider(
    int orderID, {
    required String tax,
    required String commission,
    required String totalPrice,
    required String commissionPercentage,
    required String startDate,
    required String endDate,
  }) async {
    state.value = OrderDetailsLoading();
    update();

    state.value = await provider.approveOfferAsProvider(orderID,
        tax: tax,
        commission: commission,
        totalPrice: totalPrice,
        commissionPercentage: commissionPercentage,
        startDate: startDate,
        endDate: endDate);

    update();

    if (state.value is OrderDetailsSuccess) {
      Get.toNamed(Routes.ORDER_APPROVED);
    }

    if (state.value is OrderDetailsFailed) {
      showErrorToast(state.value.errorMessage!);
    }
  }

  void initHowManyCloth() {
    howManyCloth.clear();
    if (bookingData.orderData?.types != null) {
      for (final mainType in bookingData.orderData!.types!) {
        log('Typddde ${mainType.type!.id} ${mainType.amount}');
        howManyCloth[mainType.type!.id!.toInt()] =
            mainType.amount?.toInt() ?? 0;
      }

      services.value = bookingData.orderData!.types!
          .map((e) => (e.type!.id!.toInt(), e.type!.name))
          .toList();

      update();
    }
  }

  //? show edit how many cloth widget
  void showEditClothCount() {
    initHowManyCloth();

    Get.bottomSheet(
      const Material(child: EditOrdersHowManyClothes()),
    );
  }

  Future<void> editOrderClothes() async {
    state.value = OrderDetailsLoading();
    update();

    state.value = await provider.editClothOrderAsProvider(
      bookingData,
      howManyCloth: howManyCloth,
    );

    update();

    if (state.value is OrderDetailsSuccess) {
      Get.offNamed(Routes.HOME);

      showSuccessToast(
        Get.find<LanguageController>().keys.value.orderEditedSuccessfully!,
      );
    }

    if (state.value is OrderDetailsFailed) {
      showErrorToast(state.value.errorMessage!);
    }
  }

  void confirmOrderAsUser() async {
    // state.value = OrderDetailsLoading();
    // update();

    // state.value = await provider.approveOrderAsUser(bookingData.id!.toInt());
    // update();

    // if (state.value is OrderDetailsSuccess) {
    //   // await launchUrl(Uri.parse(state.value.paymentLink!));
    //   // Get.toNamed(Routes.ORDER_APPROVED);

    //   Get.toNamed(
    //     Routes.PAYMENT,
    //     arguments: {'order': bookingData},
    //   );
    // }

    // if (state.value is OrderDetailsFailed) {
    //   showErrorToast(state.value.errorMessage!);
    // }
    Get.toNamed(
      Routes.PAYMENT,
      arguments: {
        'order': bookingData,
        'payDeposit': true,
      },
    );
  }

  void payRestOfFatora() async {
    // state.value = OrderDetailsLoading();
    // update();

    // state.value = await provider.payRestOfTotalPrice(bookingData.id!.toInt());
    // update();

    // if (state.value is OrderDetailsSuccess) {
    //   await launchUrl(Uri.parse(state.value.paymentLink!));
    //   Get.toNamed(Routes.PAYMENT_COMPELETED);
    // }

    // if (state.value is OrderDetailsFailed) {
    //   showErrorToast(state.value.errorMessage!);
    // }
    Get.toNamed(
      Routes.PAYMENT,
      arguments: {
        'order': bookingData,
        'payDeposit': false,
      },
    );
  }

  void markOrderAsCompeleted() async {
    state.value = OrderDetailsLoading();
    update();

    state.value =
        await provider.markAsCompeletedByProvider(bookingData.id!.toInt());
    update();

    if (state.value is OrderDetailsSuccess) {
      Get.toNamed(Routes.ORDER_COMPELETED);
    }

    if (state.value is OrderDetailsFailed) {
      showErrorToast(state.value.errorMessage!);
    }
  }

  void updateTrackingStatus(int index) async {
    state.value = await provider.updateTrackingStatus(bookingData.id!.toInt(),
        bookingData.orderData!.trackingSteps![index].id!.toInt());
    update();

    if (state.value is OrderDetailsSuccess) {
      bookingData.orderData!.trackingSteps![index].isStepCompleted =
          !bookingData.orderData!.trackingSteps![index].isStepCompleted!;
    }
  }

  void rateOrder() {
    Get.toNamed(
      Routes.RATE_PROVIDER,
      arguments: {
        'order': bookingData.toJson(),
      },
    );
  }

  void showCancelBottomSheet() {
    Get.bottomSheet(
      SafeArea(
        child: Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(45),
                topRight: Radius.circular(45),
              )),
          height: Get.height * 0.4,
          padding: const EdgeInsets.all(15),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Image.asset(
                      'assets/images/wallet.png',
                      width: 100,
                      height: 100,
                      fit: BoxFit.fill,
                    ),
                    Text(
                      Get.find<LanguageController>()
                          .keys
                          .value
                          .ifYouCancelYouLoseMoney!,
                      style: big2TextStyle,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  CustomButton(
                    label: Get.find<LanguageController>().keys.value.confirm!,
                    onTap: isUser()
                        ? () => cancelOrderAsUser(bookingData.id!.toInt())
                        : () => cancelOrderAsProvider(bookingData.id!.toInt()),
                    height: 50,
                    width: Get.width * 0.4,
                  ),
                  CustomButton(
                    label: Get.find<LanguageController>().keys.value.cancel!,
                    onTap: () => Get.back(),
                    height: 50,
                    width: Get.width * 0.4,
                    color: Colors.red,
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  void requestExtraTime() {
    final extraTime = TextEditingController();
    final key = GlobalKey<FormState>();
    log('aasfeeeeasfsfsfa ${bookingData.pendingPricingRequest?.toJson()}');
    Get.dialog(
      Scaffold(
        backgroundColor: Colors.transparent,
        body: GetBuilder<OrderDetailsController>(
          builder: (controller) {
            return Form(
              key: key,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(10),
                  height: Get.height * 0.4,
                  width: Get.width * 0.9,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.white,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: () => Get.back(),
                          child: const Icon(Icons.clear),
                        ),
                      ),
                      Text(
                        Get.find<LanguageController>()
                            .keys
                            .value
                            .requestExtraTime!,
                        style: big2TextStyle,
                      ),
                      CustomFormField(
                        keyboardType: TextInputType.number,
                        controller: extraTime,
                        hint: bookingData.orderData!.bookingData!.pricingOption!
                                    .id ==
                                1
                            ? Get.find<LanguageController>()
                                .keys
                                .value
                                .hoursNumber!
                            : Get.find<LanguageController>()
                                .keys
                                .value
                                .metersNumber!,
                        validator: (value) {
                          if (value.isEmpty) {
                            return 'Please Insert The Required';
                          }
                          return null;
                        },
                      ),
                      state.value is OrderDetailsLoading
                          ? const LoadingWidget()
                          : CustomButton(
                              label: Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .submit!,
                              onTap: () async {
                                if (key.currentState!.validate()) {
                                  state.value = OrderDetailsLoading();
                                  update();

                                  state.value = await provider.requestExtraTime(
                                    bookingData.id!.toInt(),
                                    double.parse(extraTime.text),
                                  );
                                  update();

                                  if (state.value is OrderDetailsSuccess) {
                                    Get.back();
                                  }
                                }
                              },
                              height: 50.h,
                              width: 200.w,
                            ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
      barrierDismissible: true,
    );
  }

  void showExtraTimeDialog() {
    Get.dialog(
      Scaffold(
        backgroundColor: Colors.transparent,
        body: GetBuilder<OrderDetailsController>(builder: (controller) {
          return Center(
            child: Container(
              padding: const EdgeInsets.all(10),
              height: Get.height * 0.32,
              width: Get.width * 0.9,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      '${Get.find<LanguageController>().keys.value.provider!} ${bookingData.provider!.name!} ${Get.find<LanguageController>().keys.value.requestedExtraTime} ${bookingData.pendingPricingRequest?.pricingValue} ${controller.bookingData.orderData?.bookingData?.pricingOption?.name} , ${Get.find<LanguageController>().keys.value.areYouAccept}',
                      // '${Get.find<LanguageController>().keys.value.provider!} ${bookingData.provider!.name!} ${Get.find<LanguageController>().keys.value.requestedExtraTime} ${bookingData.pendingPricingRequest?.pricingValue} ${bookingData.pendingPricingRequest?.pricingOption?.name} , ${Get.find<LanguageController>().keys.value.areYouAccept}',
                      style: big2TextStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  state.value is OrderDetailsLoading
                      ? const LoadingWidget()
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            CustomButton(
                              label: Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .accept!,
                              onTap: () async {
                                state.value = OrderDetailsLoading();
                                update();
                                log(bookingData.pendingPricingRequest!.id!
                                    .toInt()
                                    .toString());
                                log('aasfasfsfsfa ${bookingData.pendingPricingRequest?.toJson()}');
                                state.value = await provider.acceptExtraTime(
                                  bookingData.pendingPricingRequest!.id!
                                      .toInt(),
                                );
                                update();

                                if (state.value is OrderDetailsSuccess) {
                                  bookingData = state.value.bookingData!;
                                }

                                Get.offNamed(Routes.HOME);
                              },
                              height: 50.h,
                              width: 100.w,
                            ),
                            CustomButton(
                              label: Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .reject!,
                              onTap: () async {
                                state.value = OrderDetailsLoading();
                                update();

                                state.value = await provider.rejectExtraTime(
                                  bookingData.pendingPricingRequest!.id!
                                      .toInt(),
                                );
                                update();

                                if (state.value is OrderDetailsSuccess) {
                                  bookingData = state.value.bookingData!;
                                }
                                Get.back();
                              },
                              height: 50.h,
                              width: 100.w,
                            ),
                          ],
                        ),
                ],
              ),
            ),
          );
        }),
      ),
      barrierDismissible: true,
    );
  }

  void requestInvoice() async {
    state.value = OrderDetailsLoading();
    update();

    state.value = await provider.requestInvoice(
      bookingData.id!.toInt(),
    );
    update();
  }

  void showInvoice() async {
    if (!await launchUrlString(
      bookingData.invoice!,
      mode: GetPlatform.isAndroid
          ? LaunchMode.externalNonBrowserApplication
          : LaunchMode.platformDefault,
    )) {
      log('Error');
    }
  }
}
