import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_clean/app/modules/order_details/controllers/order_details_controller.dart';
import 'package:get_clean/global/constants/theme.dart';
import '../../../../../global/constants/constants.dart';
import '../../../../../global/controllers/language_controller.dart';

class TrackingWidget extends StatelessWidget {
  const TrackingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderDetailsController>(builder: (controller) {
      return Container(
        padding: const EdgeInsets.all(10),
        margin: const EdgeInsets.all(10),
        width: Get.width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: primaryColor, width: 0.5.w),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Get.find<LanguageController>().keys.value.trackYourOrder!,
              style: bigTextStyle,
            ),
            const Divider(color: primaryColor),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                for (int i = 0;
                    i < controller.bookingData.orderData!.trackingSteps!.length;
                    i++)
                  Column(
                    children: [
                      Text(
                        controller
                            .bookingData.orderData!.trackingSteps![i].name!,
                      ),
                      InkWell(
                        onTap: controller.isProvider()
                            ? () => controller.updateTrackingStatus(i)
                            : null,
                        child: Container(
                          height: 10.h,
                          width: 60.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: controller.bookingData.orderData!
                                    .trackingSteps![i].isStepCompleted!
                                ? Colors.green
                                : Colors.grey,
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ],
        ),
      );
    });
  }
}
