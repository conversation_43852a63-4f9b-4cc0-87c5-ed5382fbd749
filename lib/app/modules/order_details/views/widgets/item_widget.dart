// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';

import '../../../../../global/constants/theme.dart';

class ItemWidget extends StatelessWidget {
  final title, description, showThird, thirdTitle, isBold;
  const ItemWidget({
    Key? key,
    this.title,
    this.description,
    this.showThird = false,
    this.isBold = false,
    this.thirdTitle = '',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            title ?? '',
            style: regularTextStyle,
          ),
        ),
        Expanded(
          child: Text(description ?? '',
              style: middleGreyTextStyle.copyWith(
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
                color: isBold ? Colors.black : Colors.grey,
              )),
        ),
        if (showThird)
          Expanded(
            child: Text(
              thirdTitle,
              style: const TextStyle(color: Colors.green),
            ),
          ),
      ],
    );
  }
}
