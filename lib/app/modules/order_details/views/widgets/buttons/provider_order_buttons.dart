import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/controllers/add_new_service_controller.dart';
import 'package:get_clean/app/modules/order_details/controllers/order_details_controller.dart';
import 'package:get_clean/app/modules/provider_page_filtered/views/widgets/map_location_picker.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';

class ProviderOrderDetailsButtons extends GetView<OrderDetailsController> {
  const ProviderOrderDetailsButtons({super.key});

  @override
  Widget build(BuildContext context) {
    Widget mapIcon() {
      return GestureDetector(
          onTap: () {
            final lat = controller.bookingData.userLat ?? 5;
            final lng = controller.bookingData.userLong ?? 1;

            Get.dialog(MapLocationPicker(
              onSave: () {},
              selectedMarkers: {
                Marker(
                  markerId: const MarkerId("user location"),
                  position: LatLng(lat.toDouble(), lng.toDouble()),
                  icon: BitmapDescriptor.defaultMarker,
                ),
              },
              viewOnly: true,
            ));

            // launchUrlString('https://waze.com/ul?ll=$lat,$lng&navigate=yes',
            //     mode: LaunchMode.externalApplication);
          },
          child: const Icon(
            FontAwesomeIcons.mapMarkedAlt,
          ));
    }

    final isCloth = isClothesService(controller
        .bookingData.orderData?.bookingData?.pricingOption?.id
        ?.toInt());

    log('Booking_ID ${controller.bookingData.orderData?.bookingData?.pricingOption?.id}');

    return Column(
      children: [
        // request extra time as provider or company
        if (controller.bookingData.isConfirmed! &&
            controller.isProvider() &&
            !controller.bookingData.hasPendingPricingRequest! &&
            controller.bookingData.orderData!.trackingSteps!.isEmpty
            //           !isClothesService(
            //                           controller.choosedService.value.pricingOption?.id)
            &&
            !isClothesService(controller
                .bookingData.orderData?.bookingData?.pricingOption?.id
                ?.toInt()) &&
            !isCarService(controller
                .bookingData.orderData?.bookingData!.pricingOption?.id
                ?.toInt()) &&
            !isOffer(controller
                .bookingData.orderData?.bookingData?.pricingOption?.id
                ?.toInt()))
          CustomButton(
            label: Get.find<LanguageController>().keys.value.requestExtraTime!,
            onTap: () => controller.requestExtraTime(),
            height: 42.h,
            width: 294.w,
          ),

        if (((controller.bookingData.isPending! ||
                controller.bookingData.status == "received") &&
            controller.isProvider()))
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomButton(
                label:
                    Get.find<LanguageController>().keys.value.approveThisOrder!,
                onTap: () async {
                  if (controller.bookingData.bookedIn != null &&
                      controller.bookingData.bookedIn!.isNotEmpty &&
                      controller.isOfferOrder) {
                    Get.toNamed(Routes.CALCULATE_OFFER_PAGE, arguments: {
                      'order': controller.bookingData.toJson(),
                    });
                    return;
                  }

                  if (controller.isOfferOrder && !controller.isWaitingOffer) {
                    final pickedDate = await showDatePicker(
                      context: context,
                      initialDate: DateTime.now(),
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );
                    if (pickedDate != null) {
                      final pickedTime = await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                      );
                      if (pickedTime != null) {
                        final dateTime = DateTime(
                          pickedDate.year,
                          pickedDate.month,
                          pickedDate.day,
                          pickedTime.hour,
                          pickedTime.minute,
                        );
                        controller.bookedInDate.value.text =
                            DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
                      }

                      controller.approveOrderAsProvider();
                    }
                  } else {
                    log('asfassaf ${controller.bookingData.status}');

                    controller.approveOrderAsProvider(
                        canApproveClothAsOrder:
                            controller.bookingData.status == "received");
                  }
                },
                height: 42.h,
                width: 294.w,
              ),
              const SizedBox(width: 10),
              mapIcon(),
            ],
          ),

        //! Confirm & Edit Cloth Buttons
        if (controller.bookingData.status == "waiting_dropdawn" &&
            controller.isProvider())
          CustomButton(
            label: Get.find<LanguageController>().keys.value.edit!,
            onTap: () async {
              controller.showEditClothCount();
            },
            height: 42.h,
            width: 294.w,
          ),

        if (controller.bookingData.status == "waiting_dropdawn" &&
            controller.isProvider())
          CustomButton(
            label: Get.find<LanguageController>().keys.value.confirm!,
            onTap: () async {
              controller.confirmClothOrderAsProvider();
            },
            height: 42.h,
            width: 294.w,
          ),

        // complete order as provider or company
        if (controller.isProvider() &&
            (((controller.bookingData.isConfirmed!) && !isCloth) ||
                (isCloth && controller.bookingData.status == "delivered")))
          CustomButton(
            label: Get.find<LanguageController>().keys.value.compeleted!,
            onTap: () => controller.markOrderAsCompeleted(),
            height: 42.h,
            width: 294.w,
          ),

        // complete order as provider or company
        if ((controller.bookingData.isConfirmed!) &&
            controller.isProvider() &&
            isClothesService(controller
                .bookingData.orderData?.bookingData?.pricingOption?.id
                ?.toInt()))
          CustomButton(
            label: Get.find<LanguageController>().keys.value.finishClean ??
                'Finish Clean',
            onTap: () => controller.clothFinished(),
            height: 42.h,
            width: 294.w,
          ),

        if (controller.bookingData.provider != null &&
            controller.bookingData.canBeCanceled! &&
            controller.isProvider())
          CustomButton(
            label: Get.find<LanguageController>()
                .keys
                .value
                .cancelThisAppointment!,
            onTap: () => controller.showCancelBottomSheet(),
            height: 42.h,
            width: 294.w,
          ),

        if (controller.bookingData.canBeCanceled! &&
            controller.isProvider() &&
            controller.bookingData.frequency != "once" &&
            isHoursService(controller
                .bookingData.orderData?.bookingData?.pricingOption?.id))
          OutlinedButton(
            onPressed: () => controller.showCancelBottomSheet(),
            style: ButtonStyle(
                fixedSize: MaterialStateProperty.resolveWith(
                  (states) => Size(294.w, 42.h),
                ),
                side: MaterialStateProperty.resolveWith(
                  (states) => BorderSide(color: primaryColor, width: 1.w),
                ),
                shape: MaterialStateProperty.resolveWith(
                  (states) => RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                )),
            child: Text(
              Get.find<LanguageController>().keys.value.cancelAllAppointments!,
              style: regularTextStyle,
            ),
          ),
      ],
    );
  }
}

//? Notes
//waiting, waiting
// for visit -> pending && bookedIn != null
// unpaid

//? when order hour if no what time do not click done

//? check back if time not include what time, don't show (check getting time api)
