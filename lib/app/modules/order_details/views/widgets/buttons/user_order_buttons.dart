import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/order_details/controllers/order_details_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/custom_button.dart';

import '../../../../add_new_service/controllers/add_new_service_controller.dart';

class UserOrderDetailsButtons extends GetView<OrderDetailsController> {
  const UserOrderDetailsButtons({super.key});

  @override
  Widget build(BuildContext context) {
    log('afsasff ${controller.bookingData.paidAmount}');
    return Column(
      children: [
        if (controller.bookingData.canRequestInvoice! && controller.isUser())
          CustomButton(
            label: Get.find<LanguageController>().keys.value.requestInvoice!,
            onTap: () => controller.requestInvoice(),
            height: 42.h,
            width: 294.w,
          ),
        if (controller.bookingData.invoice != null && controller.isUser())
          CustomButton(
            label: Get.find<LanguageController>().keys.value.showInvoice!,
            onTap: () => controller.showInvoice(),
            height: 42.h,
            width: 294.w,
          ),
        // rate order as user
        if (controller.bookingData.isCompleted! && controller.isUser())
          CustomButton(
            label: Get.find<LanguageController>().keys.value.rate!,
            onTap: () => controller.rateOrder(),
            height: 42.h,
            width: 294.w,
          ),

        // confirm order as user
        if (controller.isUser() &&
            controller.bookingData.totalPrice !=
                controller.bookingData.paidAmount &&
            (controller.bookingData.isConfirmed == true ||
                controller.bookingData.status == "delivered"))
          CustomButton(
            label: Get.find<LanguageController>().keys.value.pay!,
            onTap: () => controller.payRestOfFatora(),
            height: 42.h,
            width: 294.w,
          ),

        // confirm order as user
        if (controller.bookingData.isApproved! && controller.isUser())
          CustomButton(
            label: Get.find<LanguageController>().keys.value.confirm!,
            onTap: () => controller.confirmOrderAsUser(),
            height: 42.h,
            width: 294.w,
          ),

        if (controller.bookingData.canBeCanceled! && controller.isUser())
          CustomButton(
            label: Get.find<LanguageController>()
                .keys
                .value
                .cancelThisAppointment!,
            onTap: () => controller.showCancelBottomSheet(),
            height: 42.h,
            width: 294.w,
          ),

        if (controller.bookingData.status == "canceled_not_delivered" &&
            controller.isUser())
          CustomButton(
            color: Colors.red,
            label: Get.find<LanguageController>().keys.value.cancel!,
            onTap: () => controller.cancelOrderAsUser(
              controller.bookingData.id!,
              isOrderChange: true,
            ),
            height: 42.h,
            width: 140.w,
          ),
        if (controller.bookingData.canBeCanceled! &&
            controller.isUser() &&
            (controller.bookingData.frequency ?? '') != "once"
    &&
    isHoursService(controller
        .bookingData.orderData?.bookingData?.pricingOption?.id))
          OutlinedButton(
            onPressed: () => controller.showCancelBottomSheet(),
            style: ButtonStyle(
                fixedSize: MaterialStateProperty.resolveWith(
                  (states) => Size(294.w, 42.h),
                ),
                side: MaterialStateProperty.resolveWith(
                  (states) => BorderSide(color: primaryColor, width: 1.w),
                ),
                shape: MaterialStateProperty.resolveWith(
                  (states) => RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                )),
            child: Text(
              Get.find<LanguageController>().keys.value.cancelAllAppointments!,
              style: regularTextStyle,
            ),
          ),

        if (controller.isUser() &&
            controller.bookingData.status == "waiting_approval") ...[
          //show approve & cancel button
          CustomButton(
            label: Get.find<LanguageController>().keys.value.approveThisOrder!,
            onTap: () => controller.approveOrderAsUser(),
            height: 42.h,
            width: 200.w,
          ),
          SizedBox(width: 10.w),
          CustomButton(
            color: Colors.red,
            label: Get.find<LanguageController>().keys.value.cancel!,
            onTap: () => controller.cancelOrderAsUser(
              controller.bookingData.id!,
              isOrderChange: true,
            ),
            height: 42.h,
            width: 140.w,
          ),
        ]
      ],
    );
  }
}
