import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/controllers/add_new_service_controller.dart';
import 'package:get_clean/app/modules/my_orders/controllers/my_orders_controller.dart';
import 'package:get_clean/app/modules/order_details/controllers/state/order_details_states.dart';
import 'package:get_clean/app/modules/order_details/views/widgets/buttons/provider_order_buttons.dart';
import 'package:get_clean/app/modules/order_details/views/widgets/order_schedule_widget.dart';
import 'package:get_clean/app/modules/order_details/views/widgets/tracking_widget.dart';
import 'package:get_clean/global/widget/loading_widget.dart';
import 'package:get_clean/global/widget/order_widget.dart';
import 'package:get_clean/global/widget/review_widget.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/order_details_controller.dart';
import 'widgets/buttons/user_order_buttons.dart';
import 'widgets/item_widget.dart';

class OrderDetailsView extends GetView<OrderDetailsController> {
  const OrderDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderDetailsController>(
      builder: (controller) {
        Widget withMaterialWidget() {
          if (isClothesService(controller
              .bookingData.orderData?.bookingData?.pricingOption?.id
              ?.toInt())) {
            return ItemWidget(
              title: Get.find<LanguageController>().keys.value.deliver!,
              description: controller.bookingData.deliver == 1
                  ? Get.find<LanguageController>().keys.value.yes!
                  : Get.find<LanguageController>().keys.value.no!,
            );
          } else {
            return ItemWidget(
              title: Get.find<LanguageController>().keys.value.iNeedMaterial!,
              description: controller.bookingData.needMaterial!
                  ? Get.find<LanguageController>().keys.value.yes!
                  : Get.find<LanguageController>().keys.value.no!,
            );
          }
        }

        return Scaffold(
          appBar: AppBar(
            title:
                Text(Get.find<LanguageController>().keys.value.orderDetails!),
            centerTitle: true,
          ),
          body: Builder(
            builder: (context) {
              if (controller.state.value is OrderDetailsLoading) {
                return const LoadingWidget();
              }

              final notEmptyTypes =
                  controller.bookingData.orderData?.types?.isNotEmpty ?? false;

              final typeData = controller.bookingData.orderData?.types
                  ?.map((e) => '${e.type?.name ?? ''} : ${e.amount}')
                  .toList()
                  .join(', ');

              return HookBuilder(builder: (context) {
                final isGroupVisible = useState(false);
                final isLoading = useState(false);

                final group = controller.bookingData.group!;

                Get.put(MyOrdersController());

                final myOrdersController = Get.find<MyOrdersController>();

                return SingleChildScrollView(
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        margin: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(color: primaryColor, width: 0.5.w),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  ItemWidget(
                                    title: Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .serviceType!,
                                    description:
                                        '${controller.bookingData.orderData?.bookingData?.pricingOption?.name ?? ''} ${notEmptyTypes ? '($typeData)' : ''}',
                                  ),
                                  if (controller.bookingData.frequency != null)
                                    ItemWidget(
                                      title: Get.find<LanguageController>()
                                          .keys
                                          .value
                                          .frequency!,
                                      description:
                                          controller.bookingData.frequency,
                                    ),
                                  if (controller.isUser())
                                    ItemWidget(
                                      title: Get.find<LanguageController>()
                                          .keys
                                          .value
                                          .providerName!,
                                      description: controller
                                              .bookingData.provider?.name ??
                                          Get.find<LanguageController>()
                                              .keys
                                              .value
                                              .everyOne!,
                                    ),
                                  if (controller.isProvider())
                                    ItemWidget(
                                      title: Get.find<LanguageController>()
                                          .keys
                                          .value
                                          .providerName!,
                                      description:
                                          controller.bookingData.user!.name,
                                    ),

                                  // ItemWidget(
                                  //   title: Get.find<LanguageController>().keys.value.duration!,
                                  //   description:
                                  //       controller.bookingData.duration.toString(),
                                  // ),

                                  ItemWidget(
                                    title: Get.find<LanguageController>()
                                            .keys
                                            .value
                                            .address ??
                                        '',
                                    description: controller.bookingData.address,
                                  ),

                                  ItemWidget(
                                    title: Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .note!,
                                    description:
                                        controller.bookingData.userNote ?? '',
                                  ),

                                  ItemWidget(
                                    title: Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .bookingTime!,
                                    description:
                                        '${controller.bookingData.startDate ?? ''}, ${controller.bookingData.startTime ?? ''}',
                                  ),
                                  withMaterialWidget()
                                ],
                              ),
                            ),
                            if (group.isNotEmpty)
                              IconButton(
                                onPressed: () async {
                                  if (isGroupVisible.value) {
                                    isGroupVisible.value = false;

                                    return;
                                  }

                                  isLoading.value = true;

                                  await myOrdersController.getFutureByGroupId(
                                      controller.bookingData.group!);

                                  isGroupVisible.value = true;

                                  isLoading.value = false;
                                },
                                icon: Padding(
                                  padding: const EdgeInsets.only(bottom: 10),
                                  child: Icon(
                                    isGroupVisible.value
                                        ? FontAwesomeIcons.arrowUpShortWide
                                        : FontAwesomeIcons.arrowDownShortWide,
                                    size: 15,
                                    color: Colors.deepOrange,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                      if (isLoading.value) const LinearProgressIndicator(),
                      Visibility(
                        visible: isGroupVisible.value,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 10.0, horizontal: 5),
                          margin: const EdgeInsets.symmetric(vertical: 5),
                          decoration: BoxDecoration(
                            color: Colors.blueGrey.shade100,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                  'Group Orders (${myOrdersController.groupOrders[group]?.bookingData?.length ?? 0})'),
                              SizedBox(
                                height: 10.h,
                              ),
                              ListView.builder(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 6),
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemBuilder: (context, index) {
                                  final bookingData = myOrdersController
                                      .groupOrders[group]?.bookingData![index];

                                  return OrderWidget(
                                    backBeforeNavigate: true,
                                    bookingData: bookingData!,
                                    isUser:
                                        myOrdersController.isUser(bookingData),
                                  );
                                },
                                itemCount: myOrdersController.groupOrders[group]
                                        ?.bookingData?.length ??
                                    0,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(10),
                        margin: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(color: primaryColor, width: 0.5.w),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .paymentDetails!,
                              style: bigTextStyle,
                            ),
                            const Divider(color: primaryColor),
                            ItemWidget(
                              title: Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .finalPrice!,
                              description: (controller.isProvider()
                                      ? controller
                                          .bookingData.providerTotalPrice!
                                          .toStringAsFixed(2)
                                      : controller.bookingData.totalPrice!
                                          .toStringAsFixed(2)) +
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .ils!,
                              showThird: true,
                            ),
                            if (!controller.isProvider())
                              ItemWidget(
                                title:
                                    '${Get.find<LanguageController>().keys.value.deposit!} (${(controller.bookingData.depositPercentage! * 100).toStringAsFixed(0)}%)',
                                description: controller.bookingData.deposit!
                                        .toStringAsFixed(2) +
                                    Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .ils!,
                                showThird: true,
                                // thirdTitle: 'Done',
                              ),
                            ItemWidget(
                              title: Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .materialPrice!,
                              description: controller.bookingData.materialPrice!
                                      .toStringAsFixed(2) +
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .ils!,
                              showThird: true,
                              // thirdTitle: 'Done',
                            ),
                            if (controller.bookingData.orderData?.bookingData
                                    ?.pricingOption?.id ==
                                1)
                              ItemWidget(
                                title: Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .hoursNumber!,
                                description: controller.bookingData.duration!
                                        .toStringAsFixed(0) +
                                    Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .hour!,
                                showThird: true,
                                // thirdTitle: 'Done',
                              ),
                            if (controller.bookingData.orderData?.bookingData
                                    ?.pricingOption?.id ==
                                2)
                              ItemWidget(
                                title: Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .metersNumber!,
                                description: controller.bookingData.duration!
                                        .toStringAsFixed(0) +
                                    Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .meter!,
                                showThird: true,
                                // thirdTitle: 'Done',
                              ),
                            if (controller.bookingData.orderData?.bookingData
                                    ?.pricingOption?.id ==
                                1)
                              ItemWidget(
                                title: Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .hourPrice!,
                                description: (controller.isProvider()
                                        ? controller
                                            .bookingData.providerUnitPrice!
                                            .toStringAsFixed(2)
                                        : controller.bookingData.unitPrice!
                                            .toStringAsFixed(2)) +
                                    Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .ils!,
                                showThird: true,
                                // thirdTitle: 'Done',
                              ),
                            if (controller.bookingData.orderData?.bookingData
                                    ?.pricingOption?.id ==
                                2)
                              ItemWidget(
                                title: Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .meterPrice!,
                                description: (controller.isProvider()
                                        ? controller
                                            .bookingData.providerUnitPrice!
                                            .toStringAsFixed(2)
                                        : controller.bookingData.unitPrice!
                                            .toStringAsFixed(2)) +
                                    Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .ils!,
                                showThird: true,
                                // thirdTitle: 'Done',
                              ),
                            if (controller.bookingData.groupTotal != null &&
                                controller.bookingData.groupTotal! > 0)
                              ItemWidget(
                                title: Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .total!,
                                //groupTotal
                                description: controller.bookingData.groupTotal!
                                        .toStringAsFixed(2) +
                                    Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .ils!,
                                isBold: true,
                                thirdTitle: Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .groupTotal ??
                                    'Group Total',
                                showThird: true,
                              ),
                            // ItemWidget(
                            //   title: Get.find<LanguageController>().keys.value.cleaningMaterial!,
                            //   description: '20 ILS',
                            //   showThird: true,
                            //   // thirdTitle: 'Done',
                            // ),
                          ],
                        ),
                      ),

                      if (controller.bookingData.orderSchedule?.isNotEmpty ??
                          false)
                        const OrderScheduleWidget(),

                      if (controller
                          .bookingData.orderData!.trackingSteps!.isNotEmpty)
                        const TrackingWidget(),

                      // user review if compeleted and user leave review
                      if (controller.bookingData.isCompleted! &&
                          controller.bookingData.userReview != null)
                        ReviewWidget(
                            review: controller.bookingData.userReview!),

                      if (!controller.bookingData.isCompleted!)
                        Container(
                          margin: const EdgeInsets.all(10),
                          padding: const EdgeInsets.all(10),
                          child: Row(
                            children: [
                              Text(
                                Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .status!,
                                style: regularTextStyle,
                              ),
                              Expanded(
                                child: Text(
                                  controller
                                          .bookingData.statusDescrption?.text ??
                                      controller.bookingData.status ??
                                      '',
                                  style: TextStyle(
                                    color: controller.bookingData
                                                .statusDescrption?.color ==
                                            null
                                        ? primaryColor
                                        : Color(
                                            int.parse(
                                              controller.bookingData
                                                      .statusDescrption?.color
                                                      ?.replaceAll("#", "") ??
                                                  '',
                                              radix: 16,
                                            ),
                                          ).withOpacity(1.0),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                      ///////////////////////////////////////////////////////
                      ///                                                 ///
                      ///               Provider Buttons Section          ///
                      ///                                                 ///
                      ///////////////////////////////////////////////////////

                      const ProviderOrderDetailsButtons(),

                      ///////////////////////////////////////////////////////
                      ///                                                 ///
                      /// *             User Buttons Section              ///
                      ///                                                 ///
                      ///////////////////////////////////////////////////////

                      const UserOrderDetailsButtons()
                    ],
                  ),
                );
              });
            },
          ),
        );
      },
    );
  }
}
