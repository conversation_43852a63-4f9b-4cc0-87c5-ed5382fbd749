import 'package:get_clean/global/models/user_billings.dart';

class UserWalletState {
  String? errorMessage;
  UserBilling? wallet;
}

class UserWalletLoading extends UserWalletState {}

class UserWalletFailed extends UserWalletState {
  UserWalletFailed(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class UserWalletSuccess extends UserWalletState {
  UserWalletSuccess(UserBilling wallet) {
    this.wallet = wallet;
  }
}
