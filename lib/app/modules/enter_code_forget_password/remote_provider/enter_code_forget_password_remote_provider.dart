import 'dart:developer';

import 'package:get_clean/app/modules/enter_code_forget_password/controllers/state/enter_code_forget_password_state.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/user.dart';

class EnterCodeForgetPasswordRemoteProvider {
  DioHelper helper = DioHelper();

  Future<EnterCodeForgetPasswordState> sendCode(String code) async {
    try {
      final response = await helper.postData(
        forgetPasswordOTPURL,
        {"otp": code},
      );
      log(response.toString());
      if (response['success'] == true) {
        return EnterCodeForgetPasswordSuccessState(User.fromJson(response));
      } else {
        showErrorToast(response['message']);
        return EnterCodeForgetPasswordFailedState(response['message']);
      }
    } catch (e) {
      return EnterCodeForgetPasswordFailedState(e.toString());
    }
  }
}
