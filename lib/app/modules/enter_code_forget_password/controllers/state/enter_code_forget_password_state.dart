import 'package:get_clean/global/models/user.dart';

class EnterCodeForgetPasswordState {
  User? user;
  String? errorMessage;
}

class EnterCodeForgetPasswordSuccessState extends EnterCodeForgetPasswordState {
  EnterCodeForgetPasswordSuccessState(User user) {
    this.user = user;
  }
}

class EnterCodeForgetPasswordFailedState extends EnterCodeForgetPasswordState {
  EnterCodeForgetPasswordFailedState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class EnterCodeForgetPasswordLoadingState extends EnterCodeForgetPasswordState {
}
