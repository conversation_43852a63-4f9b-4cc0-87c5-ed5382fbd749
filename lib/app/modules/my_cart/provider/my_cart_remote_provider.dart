import 'dart:developer';

import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/app/modules/my_cart/controllers/states/my_cart_states.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/models/user_booking.dart';

class MyCartRemoteProvider {
  DioHelper helper = DioHelper();

  // Future<MyCartState> getMyCart() async {
  //   try {
  //     final response = await helper.getData(
  //       getAllCartsURL,
  //     );
  //     if (response['success'] == true) {
  //       return MyCartSuccessState(UserBookings.fromJson(response));
  //     } else {
  //       return MyCartErrorState(response['message']);
  //     }
  //   } catch (e) {
  //     log(e.toString());
  //     return MyCartErrorState(e.toString());
  //   }
  // }
  //
  Future<MyCartState> getProviderMyCartNeedAction() async {
    try {
      final response = await helper.getData(
        getOrdersNeedActionOrders,
      );
      if (response['success'] == true) {
        log('BBBBBB ${response}');
        return MyCartSuccessState(UserBookings.fromJson(response));
        // MyCartSuccessState(UserBookings.fromJson(response));
      } else {
        return MyCartErrorState(response['message']);
        // MyCartErrorState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return MyCartErrorState(e.toString());
      // MyCartErrorState(e.toString());
    }
  }

  Future<MyCartState> getProviderMyCartTODO() async {
    try {
      final response = await helper.getData(
        getOrdersTodoOrders,
      );

      if (response['success'] == true) {
        log('BBBBBB ${response}');
        return MyCartSuccessState(UserBookings.fromJson(response));
        // MyCartSuccessState(UserBookings.fromJson(response));
      } else {
        return MyCartErrorState(response['message']);
        // MyCartErrorState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return MyCartErrorState(e.toString());
      // MyCartErrorState(e.toString());
    }
  }

  Future<MyCartState> getMyCart() async {
    try {
      final response = await helper.getData(
        getNeedActionOrders,
      );
      if (response['success'] == true) {
        log('BBBBBB ${response}');
        return MyCartSuccessState(UserBookings.fromJson(response));
        // MyCartSuccessState(UserBookings.fromJson(response));
      } else {
        return MyCartErrorState(response['message']);
        // MyCartErrorState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return MyCartErrorState(e.toString());
      // MyCartErrorState(e.toString());
    }
  }

  Future<MyCartState> getUserMyCartNeedAction() async {
    try {
      final response = await helper.getData(
        getNeedActionOrders,
      );
      if (response['success'] == true) {
        log('BBBBBB ${response}');
        return MyCartSuccessState(UserBookings.fromJson(response));
        // MyCartSuccessState(UserBookings.fromJson(response));
      } else {
        return MyCartErrorState(response['message']);
        // MyCartErrorState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return MyCartErrorState(e.toString());
      // MyCartErrorState(e.toString());
    }
  }

  Future<MyCartState> getUserMyCartTODO() async {
    try {
      final response = await helper.getData(
        getTodoOrders,
      );
      if (response['success'] == true) {
        log('BBBBBB ${response}');
        return MyCartSuccessState(UserBookings.fromJson(response));
        // MyCartSuccessState(UserBookings.fromJson(response));
      } else {
        return MyCartErrorState(response['message']);
        // MyCartErrorState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return MyCartErrorState(e.toString());
      // MyCartErrorState(e.toString());
    }
  }

  Future<MyCartState> getFutureByGroupId(String id) async {
    try {
      final url =
          isProvider() ? getFutureByGroupIdURL : getFutureByUserGroupIdURL;

      final response = await helper.getData(
        url + id,
      );

      if (response['success'] == true) {
        return MyCartSuccessState(UserBookings.fromJson(response));
      } else {
        return MyCartErrorState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return MyCartErrorState(e.toString());
    }
  }
}
