import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_cart/views/cart/widgets/my_cart_tabs.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

class CartProviderTabs extends StatelessWidget {
  final bool isCart;

  const CartProviderTabs({super.key, this.isCart = false});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50.h,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Container(
          margin: const EdgeInsets.all(10),
          height: 35.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            color: Colors.white,
            border: Border.all(
              color: primaryColor,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomInkWellCart(
                orderType: 0,
                displayText:
                    Get.find<LanguageController>().keys.value.myOrders!,
                isProviderOrderType: true,
              ),
              CustomInkWellCart(
                orderType: 1,
                displayText:
                    Get.find<LanguageController>().keys.value.myBooking!,
                isProviderOrderType: true,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
