import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/enter_phone_number/controllers/states/phone_number_state.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/custom_form_field.dart';
import '../controllers/enter_phone_number_controller.dart';

class EnterPhoneNumberView extends GetView<EnterPhoneNumberController> {
  const EnterPhoneNumberView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EnterPhoneNumberController>(builder: (controller) {
      return Form(
        key: controller.formKey,
        child: Scaffold(
          body: Container(
            width: Get.width,
            height: Get.height,
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/main_background.png',
                ),
                fit: BoxFit.fill,
              ),
            ),
            child: SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      IconButton(
                        onPressed: Get.back,
                        icon: const Icon(
                          CupertinoIcons.back,
                          size: 30,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          Get.find<LanguageController>()
                              .keys
                              .value
                              .forgetPassword!
                              .replaceAll('?', ''),
                          style: bigTextStyle,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(
                    height: 50,
                  ),
                  Image.asset(
                    'assets/images/password.png',
                  ),
                  const SizedBox(
                    height: 50,
                  ),
                  Text(
                    Get.find<LanguageController>()
                        .keys
                        .value
                        .pleaseEnterPhoneNumber!,
                    style: big2TextStyle,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  CustomFormField(
                    keyboardType: Platform.isIOS
                        ? TextInputType.number
                        : TextInputType.phone,
                    onChanged: (value) {
                      controller.textPhone.value = value;
                    },
                    label: Get.find<LanguageController>()
                        .keys
                        .value
                        .yourPhoneNumber!,
                    validator: (value) {
                      if (value.isEmpty) {
                        return Get.find<LanguageController>()
                            .keys
                            .value
                            .pleaseEnterPhoneNumber!;
                      }
                      return null;
                    },
                    icon: const Text(mainPhoneCode),
                  ),
                  // CustomIntlPhoneField(
                  //   label: Get.find<LanguageController>()
                  //       .keys
                  //       .value
                  //       .yourPhoneNumber!,
                  //   controller: controller.phone,
                  //   onChanged: (PhoneNumber value) {
                  //     controller.textPhone.value = value.phoneNumber!;
                  //   },
                  // ),
                  controller.phoneNumberState.value is PhoneNumberLoadingState
                      ? const LoadingWidget()
                      : CustomButton(
                          label:
                              Get.find<LanguageController>().keys.value.next!,
                          onTap: () => controller.sendPhoneNumber(context),
                          height: 43.h,
                          width: 129.w,
                        ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
