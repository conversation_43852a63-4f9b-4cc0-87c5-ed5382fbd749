// ignore_for_file: use_build_context_synchronously

import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/enter_phone_number/controllers/states/phone_number_state.dart';
import 'package:get_clean/app/modules/enter_phone_number/provider/remote_provider.dart';
import 'package:get_storage/get_storage.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/help_functions/help_functions.dart';
import '../../../../global/widget/custom_button.dart';
import '../../../routes/app_pages.dart';

const mainPhoneCode = '+972';

class EnterPhoneNumberController extends GetxController {
  EnterPhoneRemoteProvider provider = EnterPhoneRemoteProvider();

  final phoneNumberState = PhoneNumberState().obs;

  final formKey = GlobalKey<FormState>();

  // phone authentication
  final codeController = TextEditingController();

  // final phone = TextEditingController();

  final textPhone = ''.obs;

  void sendPhoneNumber(BuildContext context) async {
    phoneNumberState.value = PhoneNumberLoadingState();
    update();

    if (textPhone.value.startsWith('0')) {
      textPhone.value = textPhone.value.replaceFirst('0', '');
    }

    phoneNumberState.value = await provider.sendPhoneNumber(
      textPhone.value.replaceAll(' ', ''),
    );
    update();

    if (phoneNumberState.value is PhoneNumberSuccessState) {
      await checkPhone(
        textPhone.value,
        context,
        phoneNumberState.value.model!.data!.accessToken!,
      );
    }
  }

  Future<void> checkPhone(
    String phone,
    BuildContext context,
    String accessToken,
  ) async {
    FirebaseAuth auth = FirebaseAuth.instance;

    await auth.verifyPhoneNumber(
      phoneNumber: '$mainPhoneCode$phone',
      timeout: const Duration(seconds: 60),
      verificationCompleted: (AuthCredential credential) async {
        UserCredential result = await auth.signInWithCredential(credential);

        User? user = result.user;

        if (user != null) {
          GetStorage().write(tokenKey, accessToken);
          Get.toNamed(
            Routes.ENTER_NEW_PASSWORD_FORGET_PASSWORD,
          );
          log('Phone Authentication Success');
        } else {
          showErrorToast("Error In Auth Phone");
          log("Error");
        }
      },
      verificationFailed: (FirebaseAuthException exception) {
        log(exception.code.toString());
        showErrorToast(exception.message.toString());
        phoneNumberState.value = PhoneNumberState();
        update();
      },
      codeSent: (String verificationId, int? forceResendingToken) async {
        await Get.bottomSheet(
          isScrollControlled: false,
          Container(
            padding: const EdgeInsets.all(10),
            width: Get.width,
            height: Get.height,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/main_background.png',
                ),
                fit: BoxFit.fill,
              ),
            ),
            child: SafeArea(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/images/spam.png',
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Text(
                        Get.find<LanguageController>()
                            .keys
                            .value
                            .verificationCode!,
                        style: bigTextStyle,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Text(
                        Get.find<LanguageController>()
                            .keys
                            .value
                            .pleaseEnterVerificationCode!,
                        style: regularTextStyle,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Directionality(
                      textDirection: TextDirection.ltr,
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: SizedBox(
                          width: Get.width * 0.8,
                          child: PinCodeTextField(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            appContext: context,
                            length: 6,
                            onChanged: onChangeOTP,
                            enablePinAutofill: true,
                            pinTheme: PinTheme(
                              shape: PinCodeFieldShape.box,
                              inactiveColor: primaryColor,
                              activeColor: primaryColor,
                              inactiveFillColor: primaryColor,
                              activeFillColor: primaryColor,
                              disabledColor: primaryColor,
                              selectedFillColor: primaryColor,
                              selectedColor: primaryColor,
                              borderRadius: BorderRadius.circular(10),
                              fieldWidth: 50.w,
                              fieldHeight: 50.h,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: CustomButton(
                        label:
                            Get.find<LanguageController>().keys.value.submit!,
                        onTap: () async {
                          final code = codeController.text.trim();
                          AuthCredential credential =
                              PhoneAuthProvider.credential(
                            verificationId: verificationId,
                            smsCode: code,
                          );

                          UserCredential result =
                              await auth.signInWithCredential(credential);

                          User? user = result.user;

                          Get.back();

                          if (user != null) {
                            GetStorage().write(tokenKey, accessToken);
                            Get.toNamed(
                              Routes.ENTER_NEW_PASSWORD_FORGET_PASSWORD,
                            );
                            log('Phone Authentication Success');
                          } else {
                            showErrorToast("Error In Auth Phone");
                            log("Error");
                          }
                        },
                        height: 50.h,
                        width: 185.w,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      codeAutoRetrievalTimeout: (value) {},
    );
  }

  void onChangeOTP(value) {
    codeController.text = value;
    update();
  }
}
