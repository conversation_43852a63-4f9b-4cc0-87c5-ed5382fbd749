import 'package:get_clean/global/models/forget_password_model.dart';

class PhoneNumberState {
  ForgetPaawordModel? model;
  String? errorMessage;
}

class PhoneNumberLoadingState extends PhoneNumberState {}

class PhoneNumberSuccessState extends PhoneNumberState {
  PhoneNumberSuccessState(ForgetPaawordModel model) {
    this.model = model;
  }
}

class PhoneNumberFailedState extends PhoneNumberState {
  PhoneNumberFailedState(String? errorMessage) {
    this.errorMessage = errorMessage;
  }
}
