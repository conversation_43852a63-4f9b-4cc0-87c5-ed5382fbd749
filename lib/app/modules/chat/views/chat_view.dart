import 'package:bubble/bubble.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/chat/controllers/states/chat_state.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/loading_widget.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/chat_controller.dart';

class ChatView extends GetView<ChatController> {
  const ChatView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        foregroundColor: primaryColor,
        title: Text(
          controller.isUser()
              ? controller.order.provider!.name!
              : controller.order.user!.name!,
        ),
        centerTitle: true,
      ),
      bottomSheet: Safe<PERSON>rea(
        child: Container(
          color: Colors.white,
          child: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
              boxShadow: [
                BoxShadow(
                  blurRadius: 3,
                  color: Colors.grey[300]!,
                )
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: controller.messageController,
                    decoration: InputDecoration(
                      hintText:
                          Get.find<LanguageController>().keys.value.message!,
                      border: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      errorBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      enabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      focusedBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      disabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      focusedErrorBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                    ),
                  ),
                ),
                // IconButton(
                //   onPressed: () {},
                //   icon: const Icon(FontAwesomeIcons.paperclip),
                // ),
                GetBuilder<ChatController>(
                  builder: (controller) {
                    return IconButton(
                      onPressed: controller.sendMessage,
                      icon: controller.chatState.value is SendMessageLoading
                          ? const LoadingWidget()
                          : const Icon(Icons.send),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      body: GetBuilder<ChatController>(builder: (controller) {
        if (controller.chatState.value is ChatLoading) {
          return const LoadingWidget();
        }
        return Center(
          child: ListView.builder(
            itemBuilder: (context, index) {
              if (controller.chatModel.value.data!.messages![index].sentByMe!) {
                return Bubble(
                  margin: const BubbleEdges.only(top: 10),
                  alignment: Alignment.topRight,
                  nip: BubbleNip.rightTop,
                  color: primaryColor,
                  child: Text(
                    controller.chatModel.value.data!.messages![index].message!,
                    style: regularWhiteTextStyle,
                    textAlign: TextAlign.right,
                  ),
                );
              }
              return Bubble(
                margin: const BubbleEdges.only(top: 10),
                alignment: Alignment.topLeft,
                nip: BubbleNip.leftTop,
                color: const Color.fromARGB(255, 241, 243, 241),
                child: Text(
                  controller.chatModel.value.data!.messages![index].message!,
                  style: regularBlackTextStyle,
                ),
              );
            },
            itemCount: controller.chatModel.value.data!.messages!.length,
          ),
        );
      }),
    );
  }
}
