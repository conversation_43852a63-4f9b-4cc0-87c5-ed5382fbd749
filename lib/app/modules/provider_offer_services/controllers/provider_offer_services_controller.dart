import 'package:get/get.dart';
import 'package:get_clean/global/models/user_booking.dart';

import '../provider/remote_provider.dart';

class ProviderOfferServicesController extends GetxController {
  final pendingOfferOrders = <BookingData>[].obs;
  final waitingOfferOrders = <BookingData>[].obs;
  final unPaidOfferOrders = <BookingData>[].obs;

  final loading = false.obs;

  @override
  void onInit() {
    super.onInit();

    getProviderOfferBooking();
  }

  final provider = ProviderOfferServiceRemoteProvider();

  void getProviderOfferBooking() async {
    loading.value = true;

    //? filter orders by offer service
    final pendingOffers = await provider.getPendingOffers();
    final waitingOffers = await provider.getWaitingOffers();
    final unPaidOfferOffers = await provider.getUnPaidOffers();

    pendingOfferOrders.value = pendingOffers?.bookingData ?? [];

    waitingOfferOrders.value = waitingOffers?.bookingData ?? [];

    unPaidOfferOrders.value = unPaidOfferOffers?.bookingData ?? [];

    loading.value = false;

    update();
  }

  // * Change Tab Index
  final tabIndex = 0.obs;

  void onChangeIndex(int index) {
    tabIndex.value = index;
    update();
  }
}
