import 'dart:developer';

import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/user_booking.dart';

class ProviderOfferServiceRemoteProvider {
  DioHelper helper = DioHelper();

  Future<UserBookings?> getPendingOffers() async {
    try {
      final response = await helper.getData(
        getPendingProviderServiceOffersURL,
      );
      if (response['success'] == true) {
        return UserBookings.fromJson(response);
      } else {
        return null;
      }
    } catch (e) {
      log(e.toString());
      return null;
    }
  }

  Future<UserBookings?> getWaitingOffers() async {
    try {
      final response = await helper.getData(
        getWaitingProviderServiceOffersURL,
      );
      if (response['success'] == true) {
        return UserBookings.fromJson(response);
      } else {
        return null;
      }
    } catch (e) {
      log(e.toString());
      return null;
    }
  }

  Future<UserBookings?> getUnPaidOffers() async {
    try {
      final response = await helper.getData(
        getUnpaidProviderServiceOffersURL,
      );
      if (response['success'] == true) {
        return UserBookings.fromJson(response);
      } else {
        return null;
      }
    } catch (e) {
      log(e.toString());
      return null;
    }
  }

  Future<bool> deleteProviderOfferService(int providerOfferServiceID) async {
    try {
      final response = await helper.postData(
        '',
        // deleteProviderOfferServiceURL + providerOfferServiceID.toString(),
        {'': null},
      );

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}
