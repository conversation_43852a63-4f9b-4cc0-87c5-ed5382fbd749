import 'package:get_clean/global/models/providers.dart';

class ProvidersState {
  Providers? providers;
  String? errorMessage;
}

class ProvidersLoadingState extends ProvidersState {}

class AurgentRequestLoadingState extends ProvidersState {}

class AurgentRequestSuccessState extends ProvidersState {}

class ProvidersSuccessState extends ProvidersState {
  ProvidersSuccessState(Providers providers) {
    this.providers = providers;
  }
}

class ProvidersErrorState extends ProvidersState {
  ProvidersErrorState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}
