import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/service_providers/controllers/service_providers_controller.dart';
import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';

class ServiceProvidersTopRow extends StatelessWidget {
  const ServiceProvidersTopRow({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ServiceProvidersController>(builder: (controller) {
      return Container(
        padding: const EdgeInsets.all(10),
        child: Row(
          children: [
            Expanded(
              child: TextFormField(
                  onFieldSubmitted: controller.onSubmitSearch,
                  style: regularWhiteTextStyle,
                  decoration: InputDecoration(
                    hintText: Get.find<LanguageController>().keys.value.search!,
                    hintStyle: middleWhiteTextStyle,
                    filled: true,
                    fillColor: primaryColor,
                    suffixIcon: InkWell(
                      onTap: () {},
                      child: const Padding(
                        padding: EdgeInsets.only(left: 10.0, right: 10.0),
                        child: Icon(
                          Icons.search,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    iconColor: primaryColor,
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 5,
                      horizontal: 15,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(
                        style: BorderStyle.none,
                        color: Colors.transparent,
                      ),
                      gapPadding: 5,
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(
                        style: BorderStyle.none,
                        color: Colors.transparent,
                      ),
                      gapPadding: 5,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(
                        style: BorderStyle.none,
                        color: Colors.transparent,
                      ),
                      gapPadding: 5,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(
                        style: BorderStyle.none,
                        color: Colors.transparent,
                      ),
                      gapPadding: 5,
                    ),
                    disabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(
                        style: BorderStyle.none,
                        color: Colors.transparent,
                      ),
                      gapPadding: 5,
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(
                        style: BorderStyle.none,
                        color: Colors.transparent,
                      ),
                      gapPadding: 5,
                    ),
                  )),
            ),
            const SizedBox(width: 10),
            InkWell(
              onTap: controller.showAurgentBottomSheet,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: Colors.white,
                  boxShadow: const [
                    BoxShadow(color: Colors.grey, blurRadius: 1),
                  ],
                ),
                child: Image.asset(
                  'assets/images/siren.png',
                  width: 45,
                  height: 45,
                ),
              ),
            ),
            const SizedBox(width: 10),
            InkWell(
              onTap: controller.showFilterBottomSheet,
              child: Container(
                width: 45,
                height: 45,
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: primaryColor,
                  boxShadow: const [
                    BoxShadow(color: Colors.grey, blurRadius: 1),
                  ],
                ),
                child: Image.asset(
                  'assets/images/filter.png',
                  color: Colors.white,
                  width: 45,
                  height: 45,
                ),
              ),
            ),
          ],
        ),
        // CustomFormField(
        //   hint: Get.find<LanguageController>().keys.value.search!,
        //   suffixIcon: Icon(
        //     FontAwesomeIcons.sliders,
        //     size: 25,
        //   ),
        //   onSuffixIconTap: controller.showFilterBottomSheet,
        // ),
      );
    });
  }
}
