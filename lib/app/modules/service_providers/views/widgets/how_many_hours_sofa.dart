import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../add_new_service/views/widgets/add_service_form_field.dart';
import '../../controllers/service_providers_controller.dart';

class HowManyHoursSofa extends StatelessWidget {
  const HowManyHoursSofa({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ServiceProvidersController>(builder: (controller) {
      return Container(
        decoration: BoxDecoration(
          // border: Border.all(
          //   color: primaryColor,
          //   width: 0.5,
          // ),
          borderRadius: BorderRadius.circular(25),
          color: Colors.white,
        ),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: primaryColor,
                  width: 0.5,
                ),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(25),
                  topLeft: Radius.circular(25),
                ),
                color: primaryColor,
              ),
              alignment: Alignment.center,
              child: Text(
                Get.find<LanguageController>().keys.value.sofas!,
                style: regularWhiteTextStyle,
              ),
            ),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      // TextWithBackground(
                      //   color: primaryColor,
                      //   text: Get.find<LanguageController>().keys.value.sofaType!,
                      // ),
                      for (int i = 0;
                          i <
                              controller.serviceFromGlobal.value.typesSettings!
                                  .length;
                          i++)
                        AddServiceFormField(
                          keyboardType: TextInputType.text,
                          initialValue: controller
                              .serviceFromGlobal.value.typesSettings![i].name,
                          active: false,
                        ),
                    ],
                  ),
                ),
                SizedBox(width: 10.w),
                Expanded(
                  child: Column(
                    children: [
                      // Row(
                      //   children: [
                      //     Expanded(
                      //       child: TextWithBackground(
                      //         color: primaryColor,
                      //         text: Get.find<LanguageController>().keys.value.hour!,
                      //       ),
                      //     ),
                      //   ],
                      // ),
                      for (int i = 0;
                          i <
                              controller.serviceFromGlobal.value.typesSettings!
                                  .length;
                          i++)
                        Row(
                          children: [
                            Expanded(
                              child: AddServiceFormField(
                                keyboardType: TextInputType.number,
                                onChanged: (value) =>
                                    controller.onChangeHowManyHoursSofa(
                                        int.tryParse(value) ?? 0, i),
                                active: true,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
}
