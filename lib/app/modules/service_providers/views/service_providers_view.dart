import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/service_providers/controllers/states/providers_state.dart';
import 'package:get_clean/app/modules/service_providers/views/widgets/service_providers_top_row.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/bottom_navigation_bar.dart';
import 'package:get_clean/global/widget/loading_widget.dart';
import 'package:get_clean/global/widget/provider_widget.dart';
import 'package:get_clean/global/widget/text_with_background.dart';
import 'package:intl/intl.dart';

import '../../../../global/controllers/language_controller.dart';
import '../controllers/service_providers_controller.dart';

class ServiceProvidersView extends GetView<ServiceProvidersController> {
  const ServiceProvidersView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ServiceProvidersController>(builder: (controller) {
      return SafeArea(
        top: false,
        child: Scaffold(
          bottomNavigationBar: const BottomNavBarWidget(),
          appBar: AppBar(
            title: Text(controller.service.name!),
            centerTitle: true,
            elevation: 0,
          ),
          backgroundColor: primaryColor,
          body: Container(
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(45),
                topRight: Radius.circular(45),
              ),
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/main_background_bottom.png',
                ),
                fit: BoxFit.fill,
              ),
            ),
            child: Builder(builder: (context) {
              if (controller.providersState.value is ProvidersLoadingState) {
                return const LoadingWidget();
              }
              if (controller.providersState.value is ProvidersErrorState) {
                return Center(
                  child: Text(
                    controller.providersState.value.errorMessage!,
                    style: big2TextStyle,
                  ),
                );
              }
              return Column(
                children: [
                  const ServiceProvidersTopRow(),
                  if (controller.filtered.value)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        TextWithBackground(
                          color: Colors.white,
                          text: DateFormat.EEEE()
                              .format(controller.selectedDate.value),
                          textColor: primaryColor,
                          fontSize: 14.0.sp,
                        ),
                        TextWithBackground(
                          color: Colors.white,
                          text: controller.pickedTime.value,
                          textColor: primaryColor,
                          fontSize: 14.0.sp,
                        ),
                        TextWithBackground(
                          color: Colors.white,
                          text:
                              '${controller.choosedArea.value.name ?? ''},${controller.choosedCity.value.name ?? ''}',
                          textColor: primaryColor,
                          fontSize: 14.0.sp,
                        ),
                      ],
                    ),
                  Expanded(
                    child: controller.providers.value.data == null ||
                            controller.providers.value.data!.isEmpty
                        ? Center(
                            child: Text(
                              Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .noAvilableProviders!,
                              style: big2TextStyle,
                            ),
                          )
                        : ListView.builder(
                            itemBuilder: (context, index) {
                              log('asfafgggsfs ${controller.service.toJson()}');

                              return ProviderWidget(
                                provider:
                                    controller.providers.value.data![index],
                                showBooking: true,
                                isMeter: controller.isMeter,
                                isHour: controller.isHour,
                                onTap: () => controller.onProviderPressed(
                                  controller.providers.value.data![index],
                                ),
                                serviceId: controller.service?.id,
                              );
                            },
                            itemCount: controller.providers.value.data!.length,
                          ),
                  ),
                ],
              );
            }),
          ),
        ),
      );
    });
  }
}
