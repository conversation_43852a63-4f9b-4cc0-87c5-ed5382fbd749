import 'dart:developer';

import 'package:get_clean/app/modules/service_providers/controllers/states/providers_state.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/providers.dart';

class ProvidersRemoteProvider {
  DioHelper helper = DioHelper();

  Future<ProvidersState> getProviders(params, {bool isOffer = false}) async {
    log('PARAMS $params');

    try {
      const url = getAllProvidersURL;

      final response = await helper.postData(url, params);
      // final response = await helper.getData(url, params: params);
      if (response['success'] == true) {
        return ProvidersSuccessState(Providers.fromJson(response));
      } else {
        showErrorToast(response['message']);
        return ProvidersErrorState(response['message']);
      }
    } catch (e) {
      log(e.toString());

      return ProvidersErrorState(e.toString());
    }
  }

  Future<bool> sendAurgentRequest(data) async {
    try {
      final response = await helper.postData(sendAurgentRequestURL, data);
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}
