import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

import '../controllers/splash_screen_controller.dart';

class SplashScreenView extends GetView<SplashScreenController> {
  const SplashScreenView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<LanguageController>(builder: (controller) {
      return Scaffold(
        body: Center(
          child: Image.asset(
            'assets/animated/hand.gif',
            // 'assets/images/splash_image.png',
            fit: BoxFit.fill,
            width: Get.width,
            height: Get.height,
          ),
        ),
      );
    });
  }
}
