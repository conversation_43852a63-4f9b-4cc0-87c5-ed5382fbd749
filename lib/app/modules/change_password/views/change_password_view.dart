import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/custom_button.dart';
import '../controllers/change_password_controller.dart';

class ChangePasswordView extends GetView<ChangePasswordController> {
  const ChangePasswordView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChangePasswordController>(
      builder: (controller) {
        return Scaffold(
          body: Container(
            width: Get.width,
            height: Get.height,
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/main_background.png',
                ),
                fit: BoxFit.fill,
              ),
            ),
            child: Safe<PERSON><PERSON>(
              child: SingleChildScrollView(
                child: Form(
                  key: controller.key,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          IconButton(
                            onPressed: Get.back,
                            icon: const Icon(
                              CupertinoIcons.back,
                              size: 30,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .changePassword!,
                              style: bigTextStyle,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      CustomFormField(
                        label: Get.find<LanguageController>()
                            .keys
                            .value
                            .newPassword!,
                        controller: controller.passowrd,
                        keyboardType: TextInputType.text,
                        validator: (value) {
                          if (value.isEmpty) {
                            return Get.find<LanguageController>()
                                .keys
                                .value
                                .pleaseInsertPassword!;
                          }
                          return null;
                        },
                      ),
                      CustomFormField(
                        label: Get.find<LanguageController>()
                            .keys
                            .value
                            .newPasswordConfirmation!,
                        controller: controller.confirmPassword,
                        keyboardType: TextInputType.text,
                        validator: (value) {
                          if (value.isEmpty) {
                            return Get.find<LanguageController>()
                                .keys
                                .value
                                .pleaseInsertPasswordConfirmation!;
                          }
                          return null;
                        },
                      ),

                      // save data
                      Center(
                        child: CustomButton(
                          label:
                              Get.find<LanguageController>().keys.value.save!,
                          onTap: controller.changePassword,
                          height: 43.h,
                          width: 129.w,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
