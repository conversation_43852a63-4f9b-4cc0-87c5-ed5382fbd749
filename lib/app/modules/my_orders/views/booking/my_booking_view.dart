import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_orders/controllers/my_booking_controller.dart';
import 'package:get_clean/app/modules/my_orders/controllers/states/my_orders_states.dart';
import 'package:get_clean/app/modules/my_orders/views/booking/widgets/booking_tabs.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/widget/order_widget.dart';

import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/widget/loading_widget.dart';

class MyBookingsView extends GetView<MyBookingController> {
  const MyBookingsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MyBookingController>(builder: (controller) {
      return Scaffold(
        appBar: AppBar(
          title: Text(Get.find<LanguageController>().keys.value.myBooking!),
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: primaryColor,
          elevation: 0,
        ),
        body: Container(
          padding: const EdgeInsets.all(10),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                'assets/images/main_background_bottom.png',
              ),
              fit: BoxFit.fill,
            ),
          ),
          child: Builder(builder: (context) {
            if (controller.state.value is MyOrdersLoadingState) {
              return const LoadingWidget();
            }
            return Column(
              children: [
                const BookingTabs(),
                Expanded(
                  child: ListView.builder(
                    itemBuilder: (context, index) {
                      final bookingData = controller.filterBookings()[index];

                      return HookBuilder(builder: (context) {
                        final isGroupVisible = useState(false);
                        final isLoading = useState(false);

                        final group = bookingData.group!;

                        return Column(
                          children: [
                            Stack(
                              alignment: Alignment.bottomRight,
                              children: [
                                OrderWidget(
                                  bookingData: bookingData,
                                  isUser: controller.isUser(bookingData),
                                ),
                                if (group.isNotEmpty)
                                  IconButton(
                                    onPressed: () async {
                                      if (isGroupVisible.value) {
                                        isGroupVisible.value = false;
                                        return;
                                      }

                                      isLoading.value = true;

                                      await controller.getFutureByGroupId(
                                          bookingData.group!);

                                      isGroupVisible.value = true;

                                      isLoading.value = false;
                                    },
                                    icon: Padding(
                                      padding:
                                          const EdgeInsets.only(bottom: 10),
                                      child: Icon(
                                        isGroupVisible.value
                                            ? FontAwesomeIcons.arrowUpShortWide
                                            : FontAwesomeIcons
                                                .arrowDownShortWide,
                                        size: 15,
                                        color: Colors.deepOrange,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            if (isLoading.value)
                              const LinearProgressIndicator(),
                            Visibility(
                              visible: isGroupVisible.value,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 10.0, horizontal: 5),
                                margin: const EdgeInsets.symmetric(vertical: 5),
                                decoration: BoxDecoration(
                                  color: Colors.blueGrey.shade100,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                        'Group Bookings (${controller.groupOrders[group]?.bookingData?.length ?? 0})'),
                                    SizedBox(
                                      height: 10.h,
                                    ),
                                    ListView.builder(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6),
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      itemBuilder: (context, index) {
                                        final bookingData = controller
                                            .groupOrders[group]
                                            ?.bookingData![index];

                                        return OrderWidget(
                                          bookingData: bookingData!,
                                          isUser:
                                              controller.isUser(bookingData),
                                        );
                                      },
                                      itemCount: controller.groupOrders[group]
                                              ?.bookingData?.length ??
                                          0,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      });
                    },
                    itemCount: controller.filterBookings().length,
                  ),
                ),
              ],
            );
          }),
        ),
      );
    });
  }
}
