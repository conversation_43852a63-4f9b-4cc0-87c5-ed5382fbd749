import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/enter_phone_number/controllers/enter_phone_number_controller.dart';
import 'package:get_clean/app/modules/privacy_policy/views/privacy_policy_view.dart';
import 'package:get_clean/app/modules/signup/controllers/states/signup_states.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/company_signup_widget.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/custom_drop_down_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';
import 'package:get_clean/global/widget/loading_widget.dart';
import 'package:get_clean/global/widget/provider_signup_widget.dart';
import 'package:get_clean/global/widget/user_signup_widget.dart';

import '../../../../global/controllers/language_controller.dart';
import '../../../../global/enums/user_type.dart';
import '../controllers/signup_controller.dart';

class SignupView extends GetView<SignupController> {
  const SignupView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SignupController>(builder: (controller) {
      return Scaffold(
        body: Container(
          padding: const EdgeInsets.all(10),
          width: Get.width,
          height: Get.height,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                'assets/images/main_background.png',
              ),
              fit: BoxFit.fill,
            ),
          ),
          child: SafeArea(
            child: Form(
              key: controller.formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          Get.find<LanguageController>().keys.value.signup!,
                          style: bigTextStyle,
                        ),
                      ),
                      IconButton(
                        onPressed: Get.back,
                        icon: const Icon(Icons.arrow_forward_ios_outlined),
                      ),
                    ],
                  ),
                  Text(
                    Get.find<LanguageController>().keys.value.createAnAccount!,
                    style: regularTextStyle,
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          CustomFormField(
                            keyboardType: TextInputType.text,
                            controller: controller.name,
                            label: Get.find<LanguageController>()
                                .keys
                                .value
                                .fullName!,
                            validator: (value) {
                              if (value.isEmpty) {
                                return Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .pleaseEnterFullName!;
                              }
                              return null;
                            },
                          ),
                          CustomFormField(
                            keyboardType: TextInputType.emailAddress,
                            controller: controller.email,
                            label: Get.find<LanguageController>()
                                .keys
                                .value
                                .yourEmail!,
                            // validator: (value) {
                            //   if (value.isEmpty) {
                            //     return Get.find<LanguageController>()
                            //         .keys
                            //         .value
                            //         .pleaseEnterEmail!;
                            //   }
                            //   return null;
                            // },
                          ),
                          CustomFormField(
                            keyboardType: TextInputType.phone,
                            controller: controller.phone,
                            label: Get.find<LanguageController>()
                                .keys
                                .value
                                .yourPhoneNumber!,
                            validator: (value) {
                              if (value.isEmpty) {
                                return Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .pleaseEnterPhoneNumber!;
                              }
                              return null;
                            },
                            icon: const Text(mainPhoneCode),
                            onChanged: (value) {
                              String valAfterRemove = value;
                              if (valAfterRemove.startsWith('0')) {
                                valAfterRemove = valAfterRemove.substring(1);
                              }

                              controller.phoneWithCode.value =
                                  mainPhoneCode + valAfterRemove;
                            },
                          ),
                          // CustomIntlPhoneField(
                          //   controller: controller.phone,
                          //   label: Get.find<LanguageController>()
                          //       .keys
                          //       .value
                          //       .yourPhoneNumber!,
                          //   onChanged: (PhoneNumber value) {
                          //     controller.phoneWithCode.value =
                          //         value.phoneNumber!;
                          //   },
                          // ),
                          CustomFormField(
                            keyboardType: TextInputType.text,
                            controller: controller.password,
                            label: Get.find<LanguageController>()
                                .keys
                                .value
                                .password!,
                            validator: (value) {
                              if (value.isEmpty) {
                                return Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .pleaseEnterPassword!;
                              }
                              return null;
                            },
                          ),
                          CustomFormField(
                            keyboardType: TextInputType.text,
                            label: Get.find<LanguageController>()
                                .keys
                                .value
                                .address!,
                            controller: controller.address,
                            validator: (value) {
                              if (value.isEmpty) {
                                return Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .pleaseEnterYourAddress!;
                              }
                              return null;
                            },
                          ),
                          CustomDropDownButton(
                            buttonHeight: 50.0.h,
                            buttonWidth: Get.width,
                            hint:
                                Get.find<LanguageController>().keys.value.type!,
                            value: controller.choosedUserType.value,
                            onChanged: controller.onChangeChoosedUserType,
                            label:
                                Get.find<LanguageController>().keys.value.type!,
                            items: [
                              DropdownMenuItem(
                                value: UserType.user,
                                child: Text(Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .user!),
                              ),
                              DropdownMenuItem(
                                value: UserType.provider,
                                child: Text(Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .provider!),
                              ),
                              DropdownMenuItem(
                                value: UserType.company,
                                child: Text(Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .company!),
                              ),
                            ],
                          ),
                          if (controller.choosedUserType.value ==
                              UserType.company)
                            const CompanySignupWidget(),
                          if (controller.choosedUserType.value ==
                              UserType.provider)
                            const ProviderSignupWidget(),
                          if (controller.choosedUserType.value == UserType.user)
                            const UserSignupWidget(),
                        ],
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      Checkbox(
                        value: controller.acceptPrivacy.value,
                        onChanged: null,
                        // controller.onChangeAcceptPrivacy,
                      ),
                      GestureDetector(
                        onTap: () => Get.dialog(
                          AlertDialog(
                            contentPadding: const EdgeInsets.all(0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            title: Text(
                              Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .privacyPolicy!,
                              style: bigTextStyle,
                            ),
                            content: const PrivacyPolicyView(
                              fromSignUp: true,
                            ),
                            actions: [
                              TextButton(
                                onPressed: Get.back,
                                child: Text(
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .close!,
                                  style: regularTextStyle,
                                ),
                              ),
                              // accept privacy policy
                              TextButton(
                                onPressed: () {
                                  controller.onChangeAcceptPrivacy(true);
                                  Get.back();
                                },
                                child: Text(
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .accept!,
                                  style: regularTextStyle,
                                ),
                              ),
                            ],
                          ),
                        ),
                        child: Text(
                          Get.find<LanguageController>()
                              .keys
                              .value
                              .agreeWithTermsAndPrivacy!,
                          style: regularTextStyle.copyWith(
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                  controller.signupStatus.value is SignupLoadingState
                      ? const LoadingWidget()
                      : Container(
                          alignment: Alignment.center,
                          child: CustomButton(
                            label: Get.find<LanguageController>()
                                .keys
                                .value
                                .signup!,
                            onTap: controller.acceptPrivacy.value
                                ? () => controller.onSignupPressed(context)
                                : null,
                            height: 50.h,
                            width: 185.w,
                          ),
                        ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
