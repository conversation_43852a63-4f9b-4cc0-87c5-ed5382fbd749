import 'dart:developer';

import 'package:get_clean/app/modules/signup/controllers/states/signup_states.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

import '../../../../global/models/user.dart';

class SignupRemoteDataSource {
  DioHelper helper = DioHelper();

  Future<SignupStatus> signupUser(data) async {
    try {
      final response = await helper.postData(registerURL, data);

      log(response.toString());

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return SignupSuccessState(User.fromJson(response['data']));
      } else {
        showErrorToast(response['message']);
        return SignupFailedState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return SignupFailedState(e.toString());
    }
  }

  // verify user
  Future<SignupStatus> verifyUser(data) async {
    try {
      final response = await helper.postData(verifyURL, data);

      log(response.toString());

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return SignupSuccessState(User.fromJson(response['data']));
      } else {
        showErrorToast(response['message']);
        return SignupFailedState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return SignupFailedState(e.toString());
    }
  }
}
