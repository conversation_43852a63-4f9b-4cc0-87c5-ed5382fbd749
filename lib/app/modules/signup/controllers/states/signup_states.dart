import '../../../../../../global/models/user.dart';

class SignupStatus {
  User? user;
  String? errorMessge;
}

class SignupSuccessState extends SignupStatus {
  SignupSuccessState(User user) {
    this.user = user;
  }
}

class SignupFailedState extends SignupStatus {
  SignupFailedState(String errorMessge, {User? user}) {
    this.errorMessge = errorMessge;
    this.user = user;
  }
}

class SignupLoadingState extends SignupStatus {}

class SignupIdleState extends SignupStatus {}
