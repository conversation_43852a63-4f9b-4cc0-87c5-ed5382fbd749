import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as d;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/app/modules/signup/data_source/remote_data_source.dart';
import 'package:get_clean/app/routes/app_pages.dart' show Routes;
import 'package:get_clean/global/controllers/city_and_area_controller.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/skills.dart';
import 'package:get_storage/get_storage.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/enums/user_type.dart';
import '../../../../global/models/areas.dart';
import '../../../../global/models/city.dart';
import '../../../../global/widget/custom_button.dart';
import 'states/signup_states.dart';

class SignupController extends GetxController {
  final remoteDataSource = SignupRemoteDataSource();
  final formKey = GlobalKey<FormState>();
  final storage = GetStorage();
  final signupStatus = SignupStatus().obs;

  final acceptPrivacy = false.obs;

  // global variables for all users types
  final name = TextEditingController();
  final email = TextEditingController();
  final phone = TextEditingController();
  final password = TextEditingController();
  final address = TextEditingController();

  final phoneWithCode = ''.obs;

  // phone authentication
  final codeController = TextEditingController();

  final choosedArea = Area().obs;
  final choosedCity = City().obs;

  // for only user
  final choosedUserType = UserType.user.obs;

  // for only provider
  final providerName = TextEditingController();
  final providerAddress = TextEditingController();
  final providerPhone = TextEditingController();
  final providerIdNumber = TextEditingController();
  final providerFilePath = TextEditingController();
  final providerFile = File('').obs;

  // for only company
  final companyName = TextEditingController();
  final companyAddress = TextEditingController();
  final companyPhone = TextEditingController();
  final companyIdNumber = TextEditingController();
  final companyFilePath = TextEditingController();
  final companyFile = File('').obs;

  @override
  void onInit() {
    super.onInit();
    setSkills();

    Get.find<CityAndAreaController>().getAreas();
  }

  void onChangeChoosedUserType(newType) {
    choosedUserType.value = newType;
    update();
  }

  void onChangeAcceptPrivacy(value) {
    acceptPrivacy.value = value;
    update();
  }

  void pickProviderFile() async {
    providerFile.value = await pickFile();
    providerFilePath.text = providerFile.value.path;
    update();
  }

  void pickCompanyFile() async {
    companyFile.value = await pickFile();
    companyFilePath.text = companyFile.value.path;
    update();
  }

  void onSignupPressed(BuildContext context) async {
    if (formKey.currentState!.validate()) {
      if (choosedUserType.value != UserType.user) {
        if (choosedArea.value.name == null || choosedArea.value.name!.isEmpty) {
          showErrorToast(
              Get.find<LanguageController>().keys.value.pleaseChooseArea!);
          return;
        }

        if (choosedCity.value.name == null || choosedCity.value.name!.isEmpty) {
          showErrorToast(
              Get.find<LanguageController>().keys.value.pleaseChooseCity!);
          return;
        }
      }

      signupStatus.value = SignupLoadingState();
      update();

      signup();
      await checkPhone(phoneWithCode.value, context);
    }
  }

  Future<void> signup() async {
    if (formKey.currentState!.validate()) {
      if (choosedUserType.value != UserType.user) {
        if (choosedArea.value.name == null || choosedArea.value.name!.isEmpty) {
          showErrorToast(
              Get.find<LanguageController>().keys.value.pleaseChooseArea!);
          return;
        }

        if (choosedCity.value.name == null || choosedCity.value.name!.isEmpty) {
          showErrorToast(
              Get.find<LanguageController>().keys.value.pleaseChooseCity!);
          return;
        }
      }

      signupStatus.value = SignupLoadingState();
      update();

      log('Phone: ${phoneWithCode.value.replaceAll(' ', '')}');

      final phoneWithoutCode =
          phoneWithCode.value.replaceAll('+972', '').replaceAll(' ', '');

      log('afasfsaf ${phoneWithoutCode}');

      signupStatus.value = await remoteDataSource.signupUser({
        "name": name.text,
        "email": email.text,
        "phone": phoneWithoutCode,
        'phone_code': '+972',
        "password": password.text,
        "city_id": choosedArea.value.id,
        "district_id": choosedCity.value.id,
        "address": address.text,
        "type": choosedUserType.value.getValue,

        // provider data
        if (choosedUserType.value == UserType.provider)
          "provider_name": providerName.text,
        if (choosedUserType.value == UserType.provider)
          "provider_address": providerAddress.text,
        if (choosedUserType.value == UserType.provider)
          "provider_city_id": choosedArea.value.id,
        if (choosedUserType.value == UserType.provider)
          "provider_district_id": choosedCity.value.id,
        if (choosedUserType.value == UserType.provider)
          "provider_phone": providerPhone.text,
        if (choosedUserType.value == UserType.provider &&
            providerFile.value.path.isNotEmpty)
          "provider_id_file": await d.MultipartFile.fromFile(
            providerFile.value.path,
            filename: 'ProviderFile.png',
          ),
        if (choosedUserType.value != UserType.user)
          'skills[]':
              choosedSkills.map((element) => element.id).toList().join(','),

        if (choosedUserType.value == UserType.provider)
          "provider_id_number": providerIdNumber.text,

        // company data
        if (choosedUserType.value == UserType.company)
          "provider_name": companyName.text,
        if (choosedUserType.value == UserType.company)
          "provider_address": companyAddress.text,
        if (choosedUserType.value == UserType.company)
          "provider_city_id": choosedArea.value.id,
        if (choosedUserType.value == UserType.company)
          "provider_district_id": choosedCity.value.id,
        if (choosedUserType.value == UserType.company)
          "provider_phone": companyPhone.text,
        if (choosedUserType.value == UserType.company)
          "provider_id_file": await d.MultipartFile.fromFile(
            companyFile.value.path,
            filename: 'companyFile.png',
          ),
        if (choosedUserType.value == UserType.company)
          "provider_id_number": companyIdNumber.text,
      });

      update();

      // if every thing is ok we just goto home screen
      if (signupStatus.value is SignupSuccessState) {
        // await storage.write(userKey, signupStatus.value.user!.toJson());
        // storage.write(tokenKey, signupStatus.value.user!.accessToken);
        // Get.find<GlobalValuesController>().setUserLoggedIn();
        // Get.offAllNamed(
        //   Routes.HOME,
        //   arguments: {'user': signupStatus.value.user!},
        // );
      }
      // // else we check if user is verified or not and if he is not verified we goto verification screen
      // else if (signupStatus.value is SignupFailedState) {
      //   if (signupStatus.value.user != null) {
      //     Get.toNamed(
      //       Routes.VERIFICATION_CODE,
      //       arguments: {
      //         'code': signupStatus.value.user!.otp!,
      //         'phone': signupStatus.value.user!.phone!,
      //         'user': signupStatus.value.user!,
      //       },
      //     );
      //   }
      // }
    }
  }

  Future<void> checkPhone(String phone, BuildContext context) async {
    // FirebaseAuth auth = FirebaseAuth.instance;

    log(phone);

    signupStatus.value = SignupIdleState();
    update();
    await Get.bottomSheet(
      isScrollControlled: false,
      isDismissible: false,
      Container(
        padding: const EdgeInsets.all(10),
        width: Get.width,
        height: Get.height,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/images/main_background.png',
            ),
            fit: BoxFit.fill,
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Text(
                    Get.find<LanguageController>().keys.value.verificationCode!,
                    style: bigTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Text(
                    Get.find<LanguageController>()
                        .keys
                        .value
                        .pleaseEnterVerificationCode!,
                    style: regularTextStyle,
                    textAlign: TextAlign.center,
                  ),
                ),
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: SizedBox(
                      width: Get.width * 0.8,
                      child: PinCodeTextField(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        appContext: context,
                        length: 4,
                        onChanged: onChangeOTP,
                        enablePinAutofill: true,
                        pinTheme: PinTheme(
                          shape: PinCodeFieldShape.box,
                          inactiveColor: primaryColor,
                          activeColor: primaryColor,
                          inactiveFillColor: primaryColor,
                          activeFillColor: primaryColor,
                          disabledColor: primaryColor,
                          selectedFillColor: primaryColor,
                          selectedColor: primaryColor,
                          borderRadius: BorderRadius.circular(10),
                          fieldWidth: 50.w,
                          fieldHeight: 50.h,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 40.h,
                ),
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: CustomButton(
                    label: Get.find<LanguageController>().keys.value.submit!,
                    onTap: () async {
                      try {
                        signupStatus.value = SignupIdleState();
                        update();

                        final code = codeController.text.trim();

                        // final

                        final phoneWithoutCode =
                            phone.replaceAll('+972', '').replaceAll(' ', '');

                        signupStatus.value = await remoteDataSource.verifyUser({
                          "email_phone": phoneWithoutCode,
                          "otp": code,
                        });

                        if (signupStatus.value is SignupSuccessState) {
                          await storage.write(
                              userKey, signupStatus.value.user!.toJson());
                          storage.write(
                              tokenKey, signupStatus.value.user!.accessToken);
                          Get.find<GlobalValuesController>().setUserLoggedIn();
                          Get.offAllNamed(
                            Routes.HOME,
                            arguments: {'user': signupStatus.value.user!},
                          );
                        } else {
                          signupStatus.value = SignupIdleState();
                          update();
                          showErrorToast("Error In Auth Phone");
                        }

                        // if (isVerified) {
                        //   Get.find<GlobalValuesController>().setUserLoggedIn();
                        //
                        //   Get.offAllNamed(
                        //     Routes.HOME,
                        //     arguments: {'user': signupStatus.value.user!},
                        //   );
                        //   log('Phone Authentication Success');
                        // } else {
                        //   signupStatus.value = SignupIdleState();
                        //   update();
                        //   showErrorToast("Error In Auth Phone");
                        // }

                        Get.back();
                      } catch (e) {
                        showErrorToast("Please Enter Correct Code");
                        log('Error In Auth Phone $e');
                      }
                    },
                    height: 50.h,
                    width: 185.w,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
    // await auth.verifyPhoneNumber(
    //   phoneNumber: phone,
    //   timeout: const Duration(seconds: 60),
    //   verificationCompleted: (AuthCredential credential) async {
    //     UserCredential result = await auth.signInWithCredential(credential);
    //
    //     User? user = result.user;
    //
    //     if (user != null) {
    //       signup();
    //       log('Phone Authentication Success');
    //     } else {
    //       signupStatus.value = SignupIdleState();
    //       update();
    //       showErrorToast("Error In Auth Phone");
    //       log("Error");
    //     }
    //   },
    //   verificationFailed: (FirebaseAuthException exception) {
    //     log(exception.message.toString());
    //     signupStatus.value = SignupIdleState();
    //     update();
    //   },
    //   codeSent: (String verificationId, int? forceResendingToken) async {
    //     signupStatus.value = SignupIdleState();
    //     update();
    //     await Get.bottomSheet(
    //       isScrollControlled: false,
    //       isDismissible: false,
    //       Container(
    //         padding: const EdgeInsets.all(10),
    //         width: Get.width,
    //         height: Get.height,
    //         decoration: const BoxDecoration(
    //           image: DecorationImage(
    //             image: AssetImage(
    //               'assets/images/main_background.png',
    //             ),
    //             fit: BoxFit.fill,
    //           ),
    //         ),
    //         child: SafeArea(
    //           child: SingleChildScrollView(
    //             child: Column(
    //               mainAxisAlignment: MainAxisAlignment.center,
    //               crossAxisAlignment: CrossAxisAlignment.center,
    //               children: [
    //                 Padding(
    //                   padding: const EdgeInsets.all(10.0),
    //                   child: Text(
    //                     Get.find<LanguageController>()
    //                         .keys
    //                         .value
    //                         .verificationCode!,
    //                     style: bigTextStyle,
    //                   ),
    //                 ),
    //                 Padding(
    //                   padding: const EdgeInsets.all(10.0),
    //                   child: Text(
    //                     Get.find<LanguageController>()
    //                         .keys
    //                         .value
    //                         .pleaseEnterVerificationCode!,
    //                     style: regularTextStyle,
    //                     textAlign: TextAlign.center,
    //                   ),
    //                 ),
    //                 Directionality(
    //                   textDirection: TextDirection.ltr,
    //                   child: Padding(
    //                     padding: const EdgeInsets.all(10.0),
    //                     child: SizedBox(
    //                       width: Get.width * 0.8,
    //                       child: PinCodeTextField(
    //                         mainAxisAlignment: MainAxisAlignment.spaceAround,
    //                         appContext: context,
    //                         length: 4,
    //                         onChanged: onChangeOTP,
    //                         enablePinAutofill: true,
    //                         pinTheme: PinTheme(
    //                           shape: PinCodeFieldShape.box,
    //                           inactiveColor: primaryColor,
    //                           activeColor: primaryColor,
    //                           inactiveFillColor: primaryColor,
    //                           activeFillColor: primaryColor,
    //                           disabledColor: primaryColor,
    //                           selectedFillColor: primaryColor,
    //                           selectedColor: primaryColor,
    //                           borderRadius: BorderRadius.circular(10),
    //                           fieldWidth: 50.w,
    //                           fieldHeight: 50.h,
    //                         ),
    //                       ),
    //                     ),
    //                   ),
    //                 ),
    //                 SizedBox(
    //                   height: 40.h,
    //                 ),
    //                 Padding(
    //                   padding: const EdgeInsets.all(10.0),
    //                   child: CustomButton(
    //                     label:
    //                         Get.find<LanguageController>().keys.value.submit!,
    //                     onTap: () async {
    //                       try {
    //                         final code = codeController.text.trim();
    //
    //                         AuthCredential credential =
    //                             PhoneAuthProvider.credential(
    //                           verificationId: verificationId,
    //                           smsCode: code,
    //                         );
    //
    //                         UserCredential result =
    //                             await auth.signInWithCredential(credential);
    //
    //                         User? user = result.user;
    //
    //                         if (user != null) {
    //                           signup();
    //                           log('Phone Authentication Success');
    //                         } else {
    //                           signupStatus.value = SignupIdleState();
    //                           update();
    //                           showErrorToast("Error In Auth Phone");
    //                         }
    //
    //                         Get.back();
    //                       } catch (e) {
    //                         showErrorToast("Please Enter Correct Code");
    //                         log('Error In Auth Phone $e');
    //                       }
    //                     },
    //                     height: 50.h,
    //                     width: 185.w,
    //                   ),
    //                 ),
    //               ],
    //             ),
    //           ),
    //         ),
    //       ),
    //     );
    //   },
    //   codeAutoRetrievalTimeout: (value) {},
    // );
  }

  void setChoosedArea(Area area) {
    choosedArea.value = area;

    update();
  }

  void setChoosedCity(City city) {
    choosedCity.value = city;
    update();
  }

  void onChangeOTP(value) {
    codeController.text = value;
    update();
  }

  ///////////////////////////////////////////////////////////////////////
  ///              End Of Cities And Areas Section                    ///
  ///////////////////////////////////////////////////////////////////////

  ///////////////////////////////////////////////////////////////////////
  ///                   Begin Of Skills Section                       ///
  ///////////////////////////////////////////////////////////////////////

  final globalValuesController = Get.find<GlobalValuesController>();

  final homeController = Get.find<HomeController>();

  // skills
  final choosedSkills = <Skills>[].obs;
  final shownSkills = <Skills>[].obs;

  // List<Skills> initialSkills = <Skills>[];

  void setSkills() {
    // log('afasfsaaf  ${globalValuesController.skills.value.data!}');
    // shown work zones and skills
    shownSkills.value = globalValuesController.skills.value.data!
        .map((e) => Skills.fromJson(e.toJson()))
        .toList();

    choosedSkills.value = homeController.user.value.provider?.skills != null
        ? homeController.user.value.provider!.skills!
            .map((e) => Skills.fromJson(e.toJson()))
            .toList()
        : [];

    // initialSkills = homeController.user.value.provider!.skills != null
    //     ? homeController.user.value.provider!.skills!
    //         .map((e) => Skills.fromJson(e.toJson()))
    //         .toList()
    //     : [];
  }

  void onChangeSkills(values) {
    choosedSkills.clear();

    for (var element in values) {
      final res = choosedSkills.firstWhere((e) => element.value.id == e.id,
          orElse: () => Skills());
      if (res.id == null) {
        choosedSkills.add(element.value);
      }
    }

    homeController.user.value.provider?.skills =
        choosedSkills.map((e) => Skills.fromJson(e.toJson())).toList();

    log('adaskdmdas ${choosedSkills.length}}');
  }

///////////////////////////////////////////////////////////////////////
  ///                   End Of Skills Section                         ///
///////////////////////////////////////////////////////////////////////
}
