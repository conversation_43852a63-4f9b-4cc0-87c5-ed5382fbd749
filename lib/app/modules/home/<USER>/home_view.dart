import 'package:badges/badges.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/widgets/home_offers.dart';
import 'package:get_clean/app/modules/notifications/controllers/notifications_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/bottom_navigation_bar.dart';
import 'package:get_clean/global/widget/image_slider.dart';
import 'package:get_clean/global/widget/main_cached_image.dart';
import 'package:get_clean/global/widget/service_widget.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../../global/controllers/global_values_controller.dart';
import '../../../../global/widget/custom_drawer.dart';
import '../../../routes/app_pages.dart';
import '../controllers/home_controller.dart';

//? Make icon for financial report icon in calendar page app bar for provider or company or users so for all of them
//? make button to users only that will pay if showPayment is true in summary object and unpaid_tasks > 0
//? filter by month and year in the page as param (month - year) from API
//! Viewed Data in page -> location_name, service_name, start_time, end_time, status, execution_date, hours, amount
//! Check is_grouped open show all tasks button -> schedules array, and in hour amount don't show data for it
//! view some cards for report => total_transportation, total_amount, grand_total
//? if provider or company show label of if closed payment or not, field is -> unpaid_tasks if > 0 label is غير مغلق ماليا otherwise مغلق ماليا
//? make button to users only that will pay if showPayment is true in summary object and unpaid_tasks > 0
//? Make keys update and app routes or pages update last thing you do, finish things i said with it's binding and provider and controller and name it calendar_reports feature but make it's button in calendar page

//! "message":"The route api/read-notification could not be found."
class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(NotificationsController());
    return GetBuilder<NotificationsController>(
        builder: (notificationController) {
      final isAr =
          Get.find<LanguageController>().selectedLanguage.value.slug == 'ar';

      return SafeArea(
        top: false,
        child: Scaffold(
          key: controller.scaffoldKey,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: isLoggedIn()
              ? FloatingActionButton(
                  onPressed: () => Get.toNamed(Routes.CALENDAR_JOBS_VIEW),
                  backgroundColor: primaryColor,
                  child: const Icon(
                    CupertinoIcons.calendar_today,
                    // FontAwesomeIcons.plus,
                    color: Colors.white,
                  ),
                )
              : null,
          bottomNavigationBar: const BottomNavBarWidget(),
          drawer: const CustomDrawer(),
          body: GetBuilder<GlobalValuesController>(builder: (gVController) {
            return Container(
              alignment: Alignment.center,
              width: Get.width,
              height: Get.height,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xff28529F),
                    Color(0xff001F57),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      child: Row(
                        children: [
                          InkWell(
                            onTap: controller.onTapDrawerIcon,
                            child: const Icon(
                              FontAwesomeIcons.bars,
                              color: Colors.white,
                            ),
                          ),
                          Expanded(
                            child: SizedBox(
                              height: 55.h,
                              child: Image.asset(
                                isAr
                                    ? 'assets/images/logo_ar.png'
                                    : 'assets/images/logo_en.png',
                                height: 55.h,

                                // width: 50.w,
                              ),
                            ),
                          ),
                          Row(
                            children: [
                              //? Cart
                              InkWell(
                                onTap: controller.onTapCart,
                                child: const Icon(
                                  FontAwesomeIcons.solidCalendarCheck,
                                  color: Colors.white,
                                ),
                              ).paddingOnly(
                                right: 10,
                              ),
                              if (Get.find<GlobalValuesController>()
                                      .isLoggedIn
                                      .value &&
                                  notificationController
                                          .notifications.value.data !=
                                      null)
                                InkWell(
                                  onTap: controller.onTapBell,
                                  child: Badge(
                                    showBadge:
                                        Get.find<NotificationsController>()
                                            .notifications
                                            .value
                                            .data!
                                            .where(
                                                (element) => !element.isRead!)
                                            .toList()
                                            .isNotEmpty,
                                    position: BadgePosition.topEnd(
                                      top: -15,
                                      end: -5,
                                    ),
                                    badgeStyle: const BadgeStyle(
                                      padding: EdgeInsets.all(2),
                                      elevation: 0,
                                      shape: BadgeShape.circle,
                                      badgeColor: Colors.white,
                                    ),
                                    badgeContent: Text(
                                      notificationController
                                          .notifications.value.data!
                                          .where((element) => !element.isRead!)
                                          .toList()
                                          .length
                                          .toString(),
                                      style: regularTextStyle,
                                    ),
                                    child: const Icon(
                                      FontAwesomeIcons.bell,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                            ],
                          )

                          // if (notificationController.notifications.value.data !=
                          //     null)
                          //   if (Get.find<NotificationsController>()
                          //       .notifications
                          //       .value
                          //       .data!
                          //       .where((element) => !element.isRead!)
                          //       .toList()
                          //       .isEmpty)
                          //     InkWell(
                          //       onTap: controller.onTapBell,
                          //       child: const Icon(
                          //         FontAwesomeIcons.bell,
                          //         color: Colors.white,
                          //       ),
                          //     ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Stack(
                        children: [
                          Container(
                            padding: const EdgeInsets.only(
                              top: 10,
                              right: 10,
                              left: 10,
                            ),
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(50),
                                topRight: Radius.circular(50),
                              ),
                            ),
                            child: Padding(
                              padding: EdgeInsets.only(top: 15.h),
                              child: ListView(
                                controller: controller.listViewController,
                                // crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomImageSlider(
                                    images: [
                                      ...controller.globalValuesController.home
                                          .value.data!.sliders!
                                          .map((slider) => InkWell(
                                                onTap: () {
                                                  if (slider.clickable?.type ==
                                                      "external_link") {
                                                    launchUrlString(
                                                      slider.clickable?.url ??
                                                          '',
                                                      mode: LaunchMode
                                                          .externalApplication,
                                                    );
                                                  } else if (slider.clickable
                                                              ?.type ==
                                                          "provider" ||
                                                      slider.clickable?.type ==
                                                          "company") {
                                                    Get.toNamed(
                                                      Routes
                                                          .PROVIDER_FROM_SLIDER,
                                                      arguments: {
                                                        'provider': slider
                                                            .clickable!.provider
                                                      },
                                                    );
                                                  }
                                                },
                                                child: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(20),
                                                  child: MainCachedImage(
                                                    slider.image!,
                                                  ),
                                                ),
                                              )),
                                    ],
                                  ),

                                  const SizedBox(height: 15),

                                  // Center(
                                  //   child: Wrap(
                                  //     children: controller.globalValuesController
                                  //         .home.value.data!.homeServices!
                                  //         .map(
                                  //           (service) => ServiceWidget(
                                  //             service: service,
                                  //             isHour: service.id == 1,
                                  //             isMeter: service.id == 2,
                                  //           ),
                                  //         )
                                  //         .toList(),
                                  //   ),
                                  // ),

                                  StaggeredGrid.count(
                                    crossAxisCount: 3,
                                    crossAxisSpacing: 12,
                                    mainAxisSpacing: 12,
                                    children: controller.globalValuesController
                                        .home.value.data!.homeServices!
                                        .map(
                                          (service) => ServiceWidget(
                                            service: service,
                                            isHour: service.id == 1,
                                            isMeter: service.id == 2,
                                          ),
                                        )
                                        .toList(),
                                  ),

                                  //? View All
                                  Align(
                                    alignment: isAr
                                        ? Alignment.centerLeft
                                        : Alignment.centerRight,
                                    child: TextButton(
                                      onPressed: () =>
                                          Get.toNamed(Routes.ALL_HOME_VIEW),
                                      child: Text(Get.find<LanguageController>()
                                          .keys
                                          .value
                                          .viewAll!),
                                    ),
                                  ),

                                  // ...controller.globalValuesController.home.value
                                  //     .data!.services!
                                  //     .map((e) {
                                  //   return HomeServices(
                                  //     services: e.services!,
                                  //     title: e.name!,
                                  //     isHour: e.id == 1,
                                  //     isMeter: e.id == 2,
                                  //   );
                                  // }),

                                  SizedBox(height: 10.h),

                                  // offers widgets
                                  const HomeOffers(),

                                  // popular services widget
                                  // const HomePopularServices(),
                                ],
                              ),
                            ),
                          ),
                          // Positioned(
                          //   bottom: 0,
                          //   left: 0,
                          //   right: 0,
                          //   child: ,
                          // ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
        ),
      );
    });
  }
}

//import 'package:badges/badges.dart';
// import 'package:flutter/material.dart' hide Badge;
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
// import 'package:get/get.dart';
// import 'package:get_clean/app/modules/home/<USER>/widgets/home_offers.dart';
// import 'package:get_clean/app/modules/home/<USER>/widgets/home_popular_services.dart';
// import 'package:get_clean/app/modules/notifications/controllers/notifications_controller.dart';
// import 'package:get_clean/global/constants/theme.dart';
// import 'package:get_clean/global/controllers/language_controller.dart';
// import 'package:get_clean/global/widget/bottom_navigation_bar.dart';
// import 'package:get_clean/global/widget/image_slider.dart';
// import 'package:get_clean/global/widget/main_cached_image.dart';
// import 'package:get_clean/global/widget/service_widget.dart';
// import 'package:url_launcher/url_launcher_string.dart';
//
// import '../../../../global/controllers/global_values_controller.dart';
// import '../../../../global/widget/custom_drawer.dart';
// import '../../../routes/app_pages.dart';
// import '../controllers/home_controller.dart';
//
// class HomeView extends GetView<HomeController> {
//   const HomeView({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     Get.put(NotificationsController());
//     return GetBuilder<NotificationsController>(
//         builder: (notificationController) {
//       return Scaffold(
//         key: controller.scaffoldKey,
//         bottomNavigationBar: const BottomNavBarWidget(),
//         drawer: const CustomDrawer(),
//         body: GetBuilder<GlobalValuesController>(builder: (gVController) {
//           return Container(
//             alignment: Alignment.center,
//             width: Get.width,
//             height: Get.height,
//             decoration: const BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [
//                   Color(0xff28529F),
//                   Color(0xff001F57),
//                 ],
//                 begin: Alignment.topCenter,
//                 end: Alignment.bottomCenter,
//               ),
//             ),
//             child: SafeArea(
//               child: Column(
//                 children: [
//                   Container(
//                     padding: const EdgeInsets.all(10),
//                     child: Row(
//                       children: [
//                         InkWell(
//                           onTap: controller.onTapDrawerIcon,
//                           child: const Icon(
//                             FontAwesomeIcons.bars,
//                             color: Colors.white,
//                           ),
//                         ),
//                         Expanded(
//                           child: CircleAvatar(
//                             radius: 30,
//                             backgroundColor: Colors.black.withOpacity(0.2),
//                             child: Image.asset(
//                               'assets/images/logo.png',
//                             ),
//                           ),
//                         ),
//                         Row(
//                           children: [
//                             //? Cart
//                             InkWell(
//                               onTap: controller.onTapCart,
//                               child: const Icon(
//                                 FontAwesomeIcons.solidCalendarCheck,
//                                 color: Colors.white,
//                               ),
//                             ).paddingOnly(
//                               right: 10,
//                             ),
//                             if (Get.find<GlobalValuesController>()
//                                     .isLoggedIn
//                                     .value &&
//                                 notificationController
//                                         .notifications.value.data !=
//                                     null)
//                               InkWell(
//                                 onTap: controller.onTapBell,
//                                 child: Badge(
//                                   showBadge: Get.find<NotificationsController>()
//                                       .notifications
//                                       .value
//                                       .data!
//                                       .where((element) =>
//                                           (element.isRead == false))
//                                       .toList()
//                                       .isNotEmpty,
//                                   position: BadgePosition.topEnd(
//                                     top: -15,
//                                     end: -5,
//                                   ),
//                                   badgeStyle: const BadgeStyle(
//                                     padding: EdgeInsets.all(2),
//                                     elevation: 0,
//                                     shape: BadgeShape.circle,
//                                     badgeColor: Colors.white,
//                                   ),
//                                   badgeContent: Text(
//                                     notificationController
//                                         .notifications.value.data!
//                                         .where((element) =>
//                                             element.isRead == false)
//                                         .toList()
//                                         .length
//                                         .toString(),
//                                     style: regularTextStyle,
//                                   ),
//                                   child: const Icon(
//                                     FontAwesomeIcons.bell,
//                                     color: Colors.white,
//                                   ),
//                                 ),
//                               ),
//                           ],
//                         )
//
//                         // if (notificationController.notifications.value.data !=
//                         //     null)
//                         //   if (Get.find<NotificationsController>()
//                         //       .notifications
//                         //       .value
//                         //       .data!
//                         //       .where((element) => !element.isRead!)
//                         //       .toList()
//                         //       .isEmpty)
//                         //     InkWell(
//                         //       onTap: controller.onTapBell,
//                         //       child: const Icon(
//                         //         FontAwesomeIcons.bell,
//                         //         color: Colors.white,
//                         //       ),
//                         //     ),
//                       ],
//                     ),
//                   ),
//                   Expanded(
//                     child: Stack(
//                       children: [
//                         Container(
//                           padding: const EdgeInsets.only(
//                             top: 10,
//                             right: 10,
//                             left: 10,
//                           ),
//                           decoration: const BoxDecoration(
//                             color: Colors.white,
//                             borderRadius: BorderRadius.only(
//                               topLeft: Radius.circular(50),
//                               topRight: Radius.circular(50),
//                             ),
//                           ),
//                           child: Padding(
//                             padding: EdgeInsets.only(top: 30.h),
//                             child: ListView(
//                               controller: controller.listViewController,
//                               // crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 CustomImageSlider(
//                                   images: [
//                                     ...controller.globalValuesController.home
//                                         .value.data!.sliders!
//                                         .map((slider) => InkWell(
//                                               onTap: () {
//                                                 if (slider.clickable?.type ==
//                                                     "external_link") {
//                                                   launchUrlString(
//                                                     slider.clickable!.url!,
//                                                     mode: LaunchMode
//                                                         .externalApplication,
//                                                   );
//                                                 } else if (slider
//                                                             .clickable!.type ==
//                                                         "provider" ||
//                                                     slider.clickable!.type ==
//                                                         "company") {
//                                                   Get.toNamed(
//                                                     Routes.PROVIDER_FROM_SLIDER,
//                                                     arguments: {
//                                                       'provider': slider
//                                                           .clickable!.provider
//                                                     },
//                                                   );
//                                                 }
//                                               },
//                                               child: ClipRRect(
//                                                 borderRadius:
//                                                     BorderRadius.circular(20),
//                                                 child: MainCachedImage(
//                                                   slider.image!,
//                                                 ),
//                                               ),
//                                             ))
//                                         .toList(),
//                                   ],
//                                 ),
//
//                                 const SizedBox(height: 30),
//
//                                 Wrap(
//                                   children: controller.globalValuesController
//                                       .home.value.data!.homeServices!
//                                       .map(
//                                         (service) => ServiceWidget(
//                                           service: service,
//                                           isHour: service.id == 1,
//                                           isMeter: service.id == 2,
//                                         ),
//                                       )
//                                       .toList(),
//                                 ),
//
//                                 //? View All
//                                 Align(
//                                   alignment: Alignment.centerRight,
//                                   child: TextButton(
//                                     onPressed: () =>
//                                         Get.toNamed(Routes.ALL_HOME_VIEW),
//                                     child: Text(Get.find<LanguageController>()
//                                         .keys
//                                         .value
//                                         .viewAll!),
//                                   ),
//                                 ),
//
//                                 // ...controller.globalValuesController.home.value
//                                 //     .data!.services!
//                                 //     .map((e) {
//                                 //   return HomeServices(
//                                 //     services: e.services!,
//                                 //     title: e.name!,
//                                 //     isHour: e.id == 1,
//                                 //     isMeter: e.id == 2,
//                                 //   );
//                                 // }),
//
//                                 SizedBox(height: 10.h),
//
//                                 // offers widgets
//                                 const HomeOffers(),
//
//                                 // popular services widget
//                                 const HomePopularServices(),
//                               ],
//                             ),
//                           ),
//                         ),
//                         // Positioned(
//                         //   bottom: 0,
//                         //   left: 0,
//                         //   right: 0,
//                         //   child: ,
//                         // ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           );
//         }),
//       );
//     });
//   }
// }
