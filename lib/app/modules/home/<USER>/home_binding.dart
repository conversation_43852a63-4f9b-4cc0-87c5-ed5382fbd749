import 'package:get/get.dart';
import 'package:get_clean/app/modules/notifications/controllers/notifications_controller.dart';

import '../controllers/home_controller.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<NotificationsController>(
      NotificationsController(),
    );
    Get.lazyPut<HomeController>(
      () => HomeController(),
      fenix: true,
    );
  }
}
