import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';

import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/widget/image_slider.dart';
import '../../../../routes/app_pages.dart';

class HomeOffers extends StatelessWidget {
  const HomeOffers({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GlobalValuesController>(builder: (controller) {
      if (controller.home.value.data!.offers == null ||
          controller.home.value.data!.offers!.isEmpty) {
        return const SizedBox.shrink();
      }
      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                Get.find<LanguageController>().keys.value.newOffers!,
                style: big2BlackTextStyle,
              ),
              TextButton(
                onPressed: () => Get.toNamed(
                  Routes.OFFERS,
                ),
                child: Text(Get.find<LanguageController>().keys.value.viewAll!),
              ),
            ],
          ),
          CustomImageSlider(
            images: [
              ...controller.home.value.data!.offers!
                  .map(
                    (offer) => InkWell(
                      onTap: () =>
                          Get.find<HomeController>().onOfferTapped(offer),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Image.network(
                          offer.image!,
                          fit: BoxFit.fill,
                          width: Get.width,
                          height: Get.height,
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ],
          ),
        ],
      );
    });
  }
}
