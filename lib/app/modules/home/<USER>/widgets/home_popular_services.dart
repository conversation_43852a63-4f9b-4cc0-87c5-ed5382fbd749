import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/models/provider_services.dart';

import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/global_values_controller.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/widget/popular_service_widget.dart';
import '../../../../../global/widget/provider_widget.dart';
import '../../../../routes/app_pages.dart';

class HomePopularServices extends StatelessWidget {
  const HomePopularServices({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(builder: (controller) {
      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                Get.find<LanguageController>().keys.value.popularServices!,
                style: big2BlackTextStyle,
              ),
              TextButton(
                onPressed: () => Get.toNamed(
                  Routes.ALL_POPULAR_SERVICES,
                ),
                child: Text(Get.find<LanguageController>().keys.value.viewAll!),
              ),
            ],
          ),
          SizedBox(height: 10.h),
          SizedBox(
            height: 35.h,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: controller
                  .globalValuesController.home.value.data!.popularServices!
                  .map(
                    (service) => PopularServiceWidget(
                      title: service.service!.name,
                      onTap: () {
                        controller.onChangeService(service);
                      },
                      isActive: service.service!.id ==
                          controller.choosedPopularService.value.service!.id,
                    ),
                  )
                  .toList(),
            ),
          ),

          // popular companies and providers
          if (controller.choosedPopularService.value.providers != null)
            ...controller.choosedPopularService.value.providers!
                .map(
                  (e) => ProviderWidget(
                    provider: Provider.fromJson(e.toJson()),
                    showBanner: true,
                    showBooking: true,
                    isHour: controller.choosedPopularService.value.service
                            ?.pricingOptionId ==
                        1,
                    isMeter: controller.choosedPopularService.value.service
                            ?.pricingOptionId ==
                        2,
                    serviceId:
                        controller.choosedPopularService.value.service?.id,
                    onTap: () {
                      Get.toNamed(
                        Routes.PROVIDER_PAGE,
                        arguments: {
                          'provider': Provider.fromJson(e.toJson()),
                          'service': Get.find<GlobalValuesController>()
                              .allServices
                              .value
                              .data!
                              .firstWhereOrNull(
                                (element) =>
                                    element.service?.id ==
                                    controller.choosedPopularService.value
                                        .service!.id!,
                              ),
                        },
                      );
                    },
                  ),
                )
                .toList(),

          Center(
            child: TextButton(
              child: Text(Get.find<LanguageController>().keys.value.more!),
              onPressed: () {
                Get.toNamed(
                  Routes.SERVICE_PROVIDERS,
                  arguments: {
                    'service': ProviderServices.fromJson(controller
                        .choosedPopularService.value.service!
                        .toJson()),
                    'isHour': controller.choosedPopularService.value.service
                            ?.pricingOptionId ==
                        1,
                    'isMeter': controller.choosedPopularService.value.service
                            ?.pricingOptionId ==
                        2,
                    // isMeter:
                    // isHour
                  },
                );
              },
            ),
          ),
        ],
      );
    });
  }
}
