// import 'package:flutter/material.dart';
// import 'package:flutter_credit_card/credit_card_form.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import 'package:get_clean/app/modules/payment_method/controllers/states/payment_method_states.dart';
// import 'package:get_clean/global/constants/constants.dart';
// import 'package:get_clean/global/constants/theme.dart';
// import 'package:get_clean/global/widget/custom_button.dart';
// import 'package:get_clean/global/widget/loading_widget.dart';
// import '../../../../global/controllers/language_controller.dart';
// import '../controllers/payment_method_controller.dart';
//
// class PaymentMethodView extends GetView<PaymentMethodController> {
//   const PaymentMethodView({Key? key}) : super(key: key);
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(Get.find<LanguageController>().keys.value.payment!),
//         centerTitle: true,
//       ),
//       body: GetBuilder<PaymentMethodController>(builder: (controller) {
//         if (controller.state.value is PaymentMethodLoading) {
//           return const LoadingWidget();
//         }
//         return Padding(
//           padding: const EdgeInsets.all(10.0),
//           child: SingleChildScrollView(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   Get.find<LanguageController>().keys.value.addCard!,
//                   style: bigTextStyle,
//                 ),
//                 Text(
//                   Get.find<LanguageController>().keys.value.weAccept!,
//                   style: middleGreyTextStyle,
//                 ),
//                 GetBuilder<PaymentMethodController>(builder: (controller) {
//                   return CreditCardForm(
//                     formKey: controller.formKey,
//                     obscureCvv: true,
//                     obscureNumber: true,
//                     cardNumber: controller.cardNumber.value,
//                     cvvCode: controller.cvvCode.value,
//                     isHolderNameVisible: true,
//                     isCardNumberVisible: true,
//                     isExpiryDateVisible: true,
//                     cardHolderName: controller.cardHolderName.value,
//                     expiryDate: controller.expiryDate.value,
//                     themeColor: primaryColor,
//                     textColor: primaryColor,
//                     cardNumberDecoration: InputDecoration(
//                       labelText: 'Number',
//                       hintText: 'XXXX XXXX XXXX XXXX',
//                       hintStyle: const TextStyle(color: Colors.grey),
//                       labelStyle: const TextStyle(color: primaryColor),
//                       focusedBorder: controller.border,
//                       enabledBorder: controller.border,
//                     ),
//                     expiryDateDecoration: InputDecoration(
//                       hintStyle: const TextStyle(color: Colors.grey),
//                       labelStyle: const TextStyle(color: primaryColor),
//                       focusedBorder: controller.border,
//                       enabledBorder: controller.border,
//                       labelText: 'Expired Date',
//                       hintText: 'XX/XX',
//                     ),
//                     cvvCodeDecoration: InputDecoration(
//                       hintStyle: const TextStyle(color: Colors.grey),
//                       labelStyle: const TextStyle(color: primaryColor),
//                       focusedBorder: controller.border,
//                       enabledBorder: controller.border,
//                       labelText: 'CVV',
//                       hintText: 'XXX',
//                     ),
//                     cardHolderDecoration: InputDecoration(
//                       hintStyle: const TextStyle(color: Colors.grey),
//                       labelStyle: const TextStyle(color: primaryColor),
//                       focusedBorder: controller.border,
//                       enabledBorder: controller.border,
//                       labelText: 'Card Holder',
//                     ),
//                     onCreditCardModelChange: controller.onCreditCardModelChange,
//                   );
//                 }),
//                 Row(
//                   children: [
//                     Expanded(
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             Get.find<LanguageController>()
//                                 .keys
//                                 .value
//                                 .rememberThisCard!,
//                             style: big2TextStyle,
//                           ),
//                           Text(
//                             Get.find<LanguageController>()
//                                 .keys
//                                 .value
//                                 .rememberThisCardText!,
//                             style: smallGreyTextStyle,
//                           ),
//                         ],
//                       ),
//                     ),
//                     Switch(value: true, onChanged: (v) {}),
//                   ],
//                 ),
//                 SizedBox(height: 20.h),
//                 Center(
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.spaceAround,
//                     crossAxisAlignment: CrossAxisAlignment.center,
//                     children: [
//                       CustomButton(
//                         label:
//                             Get.find<LanguageController>().keys.value.confirm!,
//                         onTap: () => controller.confirmPayment(),
//                         height: 42.h,
//                         width: 294.w,
//                       ),
//                       OutlinedButton(
//                         onPressed: () =>
//                             Get.back(), // controller.showCancelDialog,
//                         style: ButtonStyle(
//                             fixedSize: MaterialStateProperty.resolveWith(
//                               (states) => Size(294.w, 42.h),
//                             ),
//                             side: MaterialStateProperty.resolveWith(
//                               (states) =>
//                                   BorderSide(color: primaryColor, width: 1.w),
//                             ),
//                             shape: MaterialStateProperty.resolveWith(
//                               (states) => RoundedRectangleBorder(
//                                 borderRadius: BorderRadius.circular(15),
//                               ),
//                             )),
//                         child: Text(
//                           Get.find<LanguageController>().keys.value.cancel!,
//                           style: regularTextStyle,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         );
//       }),
//     );
//   }
// }
