import 'dart:developer';

import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/albums_model.dart';

class AlbumRemoteProvider {
  DioHelper helper = DioHelper();

  //? Get Album
  Future<AllAlbumsModel> getAlbum() async {
    try {
      final response = await helper.getData(getAlbums);
      if (response['success'] == true) {
        return AllAlbumsModel.fromJson(response);
      } else {
        showErrorToast(response['message']);
        return AllAlbumsModel();
      }
    } catch (e) {
      return AllAlbumsModel();
    }
  }

  //? add new album
  Future<bool> addNewAlbum({
    required String name,
    required String cover,
  }) async {
    try {
      final response = await helper.postData(
        addNewAlbumURL,
        {
          "name": name,
          // "cover": cover,
        },
        image: cover,
        fileName: 'cover',
      );

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  //? edit
  Future<bool> editAlbum(
    int id, {
    required String name,
    required String cover,
  }) async {
    try {
      final response = await helper.postData(
        editAlbumURL + id.toString(),
        {
          "name": name,
          "cover": cover,
        },
        image: cover,
        fileName: 'cover',
      );
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  //? delete
  Future<bool> deleteAlbum({
    required int? albumId,
  }) async {
    try {
      final response = await helper.deleteData(
        deleteAlbumURL + albumId.toString(),
        {"": null},
      );
      log('afasfas ${response}');

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  //? Get Album Images
  Future<List<AlbumImagesModel>> getAlbumImages(int albumID) async {
    try {
      final response =
          await helper.getData(getAlbumImagesURL + albumID.toString());
      if (response['success'] == true) {
        List<AlbumImagesModel> images = [];
        for (var item in response['data']) {
          images.add(AlbumImagesModel.fromJson(item));
        }
        return images;
      } else {
        showErrorToast(response['message']);
        return [];
      }
    } catch (e) {
      return [];
    }
  }

  //? add new album image
  Future<bool> addOneAlbumImage({
    required int? albumId,
    required String image,
  }) async {
    try {
      final response = await helper.postData(
        addNewImageURL,
        {
          "album_id": albumId,
        },
        image: image,
        fileName: 'photos[]',
      );
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  Future<bool> addAlbumImages({
    required int? albumId,
    required List<String> images,
  }) async {
    try {
      for (var image in images) {
        await addOneAlbumImage(albumId: albumId, image: image);
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  //? delete album image
  Future<bool> deleteAlbumImage({
    required int? imageId,
  }) async {
    try {
      final response = await helper.deleteData(
        deleteImageURL + imageId.toString(),
        {"": null},
      );
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}
