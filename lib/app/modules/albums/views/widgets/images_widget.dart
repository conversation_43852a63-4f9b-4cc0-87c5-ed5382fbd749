import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/albums/controllers/album_controller.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/models/albums_model.dart';

List<Widget> imagesWidget({
  required AlbumsModel? album,
  required AlbumController controller,
}) {
  return [
    const Divider(),
    Row(
      children: [
        Text(
          Get.find<LanguageController>().keys.value.images!,
          style: bigBlackTextStyle.copyWith(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
          ),
        ),

        //? Add Images
        IconButton(
          icon: const CircleAvatar(
            child: Icon(Icons.add, size: 20),
          ),
          onPressed: () {
            controller.addImagesBottomSheet(albumId: album?.id);
          },
        ),
      ],
    ),
    SizedBox(height: 10.h),
    GridView.builder(
      shrinkWrap: true,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.25,
        crossAxisSpacing: 10,
      ),
      itemCount: album?.images.length,
      itemBuilder: (context, index) {
        final image = album?.images[index];
        return Card(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 4,
          child: Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(image?.photo ?? '',
                    width: double.infinity, height: 250, fit: BoxFit.cover),
              ),

              //! Delete
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  icon: const CircleAvatar(
                    backgroundColor: Colors.red,
                    child: Icon(Icons.delete, size: 20, color: Colors.white),
                  ),
                  onPressed: () {
                    // Show dialog or bottom sheet for confirming image deletion
                    controller.onDeleteImage(imageId: image?.id);
                  },
                ),
              ),
            ],
          ),
        );
      },
    )
  ];
}
