import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/albums/views/widgets/images_widget.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

import '../controllers/album_controller.dart';

class AlbumsView extends GetView<AlbumController> {
  const AlbumsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Get.find<LanguageController>().keys.value.albums!),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/images/main_background_bottom.png',
            ),
            fit: BoxFit.fill,
          ),
        ),
        alignment: Alignment.center,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.topCenter,
              child: Obx(() {
                if (controller.album.value.albums != null) {
                  return ListView.builder(
                    itemCount: controller.album.value.albums?.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final album = controller.album.value.albums?[index];

                      return Obx(() {
                        if (album?.images == null) {
                          return const SizedBox.shrink();
                        }
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: ExpansionTile(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: const BorderSide(color: Colors.grey),
                            ),
                            childrenPadding: const EdgeInsets.only(
                              left: 8,
                              right: 8,
                              bottom: 8,
                            ),
                            title: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      SizedBox(
                                        width: 60,
                                        height: 75,
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          child: Image.network(
                                              album?.cover ?? '',
                                              width: 60,
                                              height: 75,
                                              fit: BoxFit.cover),
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                      Text(album?.name ?? '',
                                          style: bigBlackTextStyle.copyWith(
                                            fontSize: 20.sp,
                                          )),
                                    ],
                                  ),
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const CircleAvatar(
                                          child: Icon(Icons.edit, size: 20),
                                        ),
                                        onPressed: () {
                                          controller.addAlbumBottomSheet(
                                              editedAlbum: album);
                                        },
                                      ),
                                      IconButton(
                                        icon: const CircleAvatar(
                                          backgroundColor: Colors.red,
                                          child: Icon(Icons.delete,
                                              size: 20, color: Colors.white),
                                        ),
                                        onPressed: () {
                                          controller.onDeleteAlbum(
                                              albumId: album?.id);
                                        },
                                      ),
                                    ],
                                  )
                                ],
                              ),
                            ),
                            children: imagesWidget(
                              album: album,
                              controller: controller,
                            ),
                          ),
                        );
                      });
                    },
                  );
                } else {
                  return const SizedBox.shrink();
                }
              }),
            ),
            Obx(() {
              if (controller.loading.value) {
                return const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: LinearProgressIndicator(
                    backgroundColor: Colors.transparent,
                    color: primaryColor,
                  ),
                );
              } else {
                return const SizedBox.shrink();
              }
            }),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          controller.addAlbumBottomSheet();
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
