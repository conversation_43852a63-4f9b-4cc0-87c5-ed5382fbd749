import 'package:get/get.dart';
import '../../../../global/controllers/language_controller.dart';

class LanguagesController extends GetxController {
  final languageController = Get.find<LanguageController>();

  // void changeToArabic() {
  //   languageController.changeLanguageAsWeNeed(LanguageModel.ar);
  // }

  // void changeToEnglish() {
  //   languageController.changeLanguageAsWeNeed(LanguageModel.en);
  // }

  // void changeToHeb() {
  //   languageController.changeLanguageAsWeNeed(LanguageModel.heb);
  // }
}
