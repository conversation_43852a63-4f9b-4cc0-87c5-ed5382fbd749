import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/wallet/controllers/states/wallet_state.dart';
import 'package:get_clean/app/modules/wallet/provider/wallet_remote_provider.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

import '../../../../global/models/wallet_model.dart';

class WalletController extends GetxController {
  final provider = WalletRemoteProvider();
  final state = WalletState().obs;
  final allOrdersModel = DoneAndCompletedOrderModel().obs;
  final walletModel = WalletModel().obs;
  final totalUnPaidBalance = 0.0.obs;

  final choosedIndex = 0.obs;

  final invoice = File('').obs;

  @override
  void onInit() {
    super.onInit();
    getWallet();
  }

  void getWallet() async {
    state.value = WalletLoading();

    update();

    state.value = await provider.getWallet();

    if (state.value is WalletSuccess) {
      walletModel.value = state.value.wallet!;
      allOrdersModel.value = state.value.allOrders!;

      totalUnPaidBalance.value = allOrdersModel.value.notPaid?.fold(
            0.0,
            (previousValue, element) =>
                (previousValue ?? 0.0) + element.providerTotalPrice!,
          ) ??
          0.0;
    } else if (state.value is WalletFailed) {
      showErrorToast(state.value.errorMessage!);
    }
    update();
  }

  void onTapBarChange(index) {
    choosedIndex.value = index;
    update();
  }

  void uploadInvoice() async {
    invoice.value = await pickFile(
      type: FileType.any,
    );
    state.value = WalletLoading();
    update();

    state.value = await provider.uploadInvoice(invoice.value.path);
    update();
  }
}
