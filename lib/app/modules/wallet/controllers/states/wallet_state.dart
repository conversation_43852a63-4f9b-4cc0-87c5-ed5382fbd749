import 'package:get_clean/global/models/wallet_model.dart';

class WalletState {
  String? errorMessage;
  WalletModel? wallet;
  DoneAndCompletedOrderModel? allOrders;
}

class WalletLoading extends WalletState {}

class WalletFailed extends WalletState {
  WalletFailed(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class WalletSuccess extends WalletState {
  WalletSuccess(WalletModel? wallet, [DoneAndCompletedOrderModel? allOrders]) {
    this.wallet = wallet;
    this.allOrders = allOrders;
  }
}
