import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/login/controllers/states/login_states.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/custom_drop_down_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';

import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/loading_widget.dart';
import '../controllers/login_controller.dart';

class LoginView extends GetView<LoginController> {
  const LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LoginController>(
      builder: (controller) {
        return Form(
          key: controller.formKey,
          child: Scaffold(
            body: Stack(
              fit: StackFit.expand,
              children: [
                Positioned(
                  top: 0,
                  right: 0,
                  left: 0,
                  child: Container(
                    height: 376.h,
                    color: Colors.white,
                    child: Image.asset(
                      'assets/images/login_background.png',
                      fit: BoxFit.fill,
                    ),
                  ),
                ),
                Positioned(
                  top: 0,
                  right: 0,
                  left: 0,
                  child: Container(
                    height: 376.h,
                    color: primaryColor.withOpacity(0.7),
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 500.h,
                    decoration: BoxDecoration(
                      image: const DecorationImage(
                        image: AssetImage(
                          'assets/images/login_bottom_image.png',
                        ),
                        fit: BoxFit.fill,
                      ),
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(color: Colors.grey[500]!, blurRadius: 4),
                      ],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(25),
                        topRight: Radius.circular(25),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: primaryColor,
                              borderRadius: BorderRadius.circular(25),
                            ),
                            margin: const EdgeInsets.all(10),
                            height: 3.h,
                            width: 85.w,
                          ),
                          Text(
                            Get.find<LanguageController>().keys.value.login!,
                            style: bigTextStyle,
                          ),
                          CustomFormField(
                            keyboardType: TextInputType.text,
                            controller: controller.emailOrPhone,
                            hint: Get.find<LanguageController>()
                                .keys
                                .value
                                .yourPhoneNumberOrEmail,
                            icon: const Icon(FontAwesomeIcons.mobileScreen),
                            validator: (value) {
                              if (value.isEmpty) {
                                return Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .pleaseEnterEmailOrPassword;
                              }
                              return null;
                            },
                          ),
                          CustomFormField(
                            keyboardType: TextInputType.text,
                            controller: controller.password,
                            onIconTap: controller.flipShowPassword,
                            hint: Get.find<LanguageController>()
                                .keys
                                .value
                                .password,
                            icon: Icon(
                              controller.showPassword.value == false
                                  ? FontAwesomeIcons.lockOpen
                                  : FontAwesomeIcons.lock,
                            ),
                            isSecure: !controller.showPassword.value,
                            validator: (value) {
                              if (value.isEmpty) {
                                return Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .pleaseEnterPassword;
                              }
                              return null;
                            },
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Checkbox(
                                    value: controller.rememberMe.value,
                                    onChanged: (value) =>
                                        controller.flipRememberMe(),
                                  ),
                                  Text(Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .rememberMe!),
                                ],
                              ),
                              TextButton(
                                onPressed: () => Get.toNamed(
                                  Routes.ENTER_PHONE_NUMBER,
                                ),
                                child: Text(Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .forgetPassword!),
                              ),
                            ],
                          ),
                          controller.loginStatus.value is LoginLoadingState
                              ? const LoadingWidget()
                              : CustomButton(
                                  label: Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .login!,
                                  onTap: controller.onPressLogin,
                                  height: 50.0.h,
                                  width: 184.0.w,
                                ),
                          TextButton(
                            onPressed: controller.onPressSignup,
                            child: Text(Get.find<LanguageController>()
                                .keys
                                .value
                                .signup!),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CustomDropDownButton(
                                hint: Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .selectedLanguage,
                                value: controller
                                    .languageController.selectedLanguage.value,
                                onChanged: (data) {
                                  controller.languageController
                                      .changeLanguageAsWeNeed(data, context);
                                },
                                items: Get.find<LanguageController>()
                                    .languagesModel
                                    .value
                                    .data!
                                    .map(
                                      (e) => DropdownMenuItem(
                                        value: e,
                                        child: Text(e.name!),
                                      ),
                                    )
                                    .toList(),
                                buttonHeight: 50.0.h,
                                buttonWidth: 180.w,
                              ),
                              Container(
                                alignment: Alignment.bottomRight,
                                child: TextButton(
                                  onPressed: controller.onPressSkip,
                                  child: Text(Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .skip!),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
