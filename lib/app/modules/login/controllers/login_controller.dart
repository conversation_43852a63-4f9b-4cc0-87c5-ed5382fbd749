import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/login/providers/global_provider.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_storage/get_storage.dart';

import '../../../../global/controllers/global_values_controller.dart';
import '../providers/local_provider.dart';
import 'states/login_states.dart';

class LoginController extends GetxController {
  RxBool showPassword = false.obs;
  RxBool rememberMe = true.obs;

  final languageController = Get.find<LanguageController>();
  final formKey = GlobalKey<FormState>();

  final localProvider = LocalLoginProvider();
  final globalProvider = LoginGlobalProvider();
  final loginStatus = LoginStatus().obs;

  TextEditingController emailOrPhone = TextEditingController(
    // text: kDebugMode ? '<EMAIL>' : '', //! User
    text: kDebugMode ? '<EMAIL>' : '', //! User
    // text: kDebugMode ? 'a@a.a' : '', //! Provider
  );

  TextEditingController password = TextEditingController(
    text: kDebugMode ? '12345678' : '', //! User
    // text: kDebugMode ? 'password' : '', //! Provider
  );

  @override
  void onInit() {
    super.onInit();
    if (localProvider.hasData()) {
      GetStorage().remove(tokenKey);

      emailOrPhone =
          TextEditingController(text: localProvider.getEmailOrPhone());
      password = TextEditingController(text: localProvider.getPassword());
    }
  }

  void flipShowPassword() {
    showPassword.value = !showPassword.value;
    update();
  }

  void flipRememberMe() {
    rememberMe.value = !rememberMe.value;
    update();
  }

  void onPressSignup() {
    Get.toNamed(Routes.SIGNUP);
  }

  void _validatePhoneNumberWithCountryCode() {
    if (emailOrPhone.text.isPhoneNumber) {
      if (emailOrPhone.text.startsWith('+972')) return;

      if (emailOrPhone.text.startsWith('0')) {
        emailOrPhone.text = '+972${emailOrPhone.text.substring(1)}';
      } else if (emailOrPhone.text.startsWith('972')) {
        emailOrPhone.text = '+${emailOrPhone.text}';
      } else {
        emailOrPhone.text = '+972${emailOrPhone.text}';
      }
    }

    update();
  }

  void onPressLogin() async {
    if (formKey.currentState!.validate()) {
      try {
        loginStatus.value = LoginLoadingState();
        update();

        _validatePhoneNumberWithCountryCode();

        loginStatus.value = await globalProvider.login(
          emailOrPhone.text.replaceAll(' ', ''),
          password.text,
        );

        update();

        if (loginStatus.value is LoginSuccessState) {
          // if (rememberMe.value) {

          await localProvider.saveLoginData(emailOrPhone.text, password.text);
          await localProvider.saveUserData(loginStatus.value.user!);

          final globalController = Get.find<GlobalValuesController>();

          await globalController.getHome();
          // }

          log('Success');

          Get.find<GlobalValuesController>().setUserLoggedIn();
          Get.offAllNamed(
            Routes.HOME,
            arguments: {'user': loginStatus.value.user!},
          );
        }
      } catch (e) {
        log(e.toString());
      }
    }
  }

  void onPressSkip() {
    Get.offAllNamed(Routes.HOME);
  }
}
