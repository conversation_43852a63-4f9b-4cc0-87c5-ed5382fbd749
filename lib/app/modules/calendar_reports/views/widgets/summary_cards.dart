import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

import '../../controllers/calendar_reports_controller.dart';

class SummaryCards extends StatelessWidget {
  const SummaryCards({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CalendarReportsController>(
      builder: (controller) {
        final summary = controller.reportsData.value.summary;
        if (summary == null) return const SizedBox.shrink();

        return Container(
          padding: EdgeInsets.all(16.w),
          child: Column(
            children: [
              // First Row - Transportation and Amount
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryCard(
                      title: Get.find<LanguageController>().keys.value.totalTransportation ??
                          'Total Transportation',
                      value: controller.formatCurrency(summary.totalTransportation),
                      icon: Icons.local_shipping,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: _buildSummaryCard(
                      title: Get.find<LanguageController>().keys.value.totalAmount ??
                          'Total Amount',
                      value: controller.formatCurrency(summary.totalAmount),
                      icon: Icons.attach_money,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.h),
              // Second Row - Grand Total
              _buildSummaryCard(
                title: Get.find<LanguageController>().keys.value.grandTotal ??
                    'Grand Total',
                value: controller.formatCurrency(summary.grandTotal),
                icon: Icons.account_balance_wallet,
                color: primaryColor,
                isFullWidth: true,
              ),
              SizedBox(height: 12.h),
              // Third Row - Task Statistics
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryCard(
                      title: Get.find<LanguageController>().keys.value.totalTasks ??
                          'Total Tasks',
                      value: (summary.totalTasks ?? 0).toString(),
                      icon: Icons.task,
                      color: Colors.orange,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: _buildSummaryCard(
                      title: Get.find<LanguageController>().keys.value.totalHours ??
                          'Total Hours',
                      value: (summary.totalHours ?? 0).toString(),
                      icon: Icons.access_time,
                      color: Colors.purple,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    bool isFullWidth = false,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20.sp,
                ),
              ),
              if (isFullWidth) ...[
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ],
          ),
          if (!isFullWidth) ...[
            SizedBox(height: 8.h),
            Text(
              title,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ],
          SizedBox(height: 4.h),
          Text(
            value,
            style: TextStyle(
              fontSize: isFullWidth ? 20.sp : 18.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
