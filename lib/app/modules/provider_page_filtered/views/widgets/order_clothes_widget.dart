import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/provider_page_filtered_controller.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../add_new_service/views/widgets/add_service_form_field.dart';

class OrdersHowManyClothes extends StatelessWidget {
  const OrdersHowManyClothes({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProviderPageFilteredController>(builder: (controller) {
      //! To Get Only The Services That Have Price From The Provider
      final services = controller.provider.services!
          .firstWhereOrNull((element) => element.id == controller.service.id)!
          .pricingList!
          .where((element) => element.price != null && element.price != 0)
          .toList();

      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          color: Colors.white,
        ),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: primaryColor,
                  width: 0.5,
                ),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(25),
                  topLeft: Radius.circular(25),
                ),
                color: primaryColor,
              ),
              alignment: Alignment.center,
              child: Text(
                Get.find<LanguageController>().keys.value.sofas!,
                style: regularWhiteTextStyle,
              ),
            ),
            for (int i = 0; i < services.length; i++)
              if (controller.howManyCloth[services[i].typeModel?.id] != null)
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          AddServiceFormField(
                            keyboardType: TextInputType.text,
                            initialValue: services[i].typeModel?.name.text,
                            // controller
                            //     .serviceFromGlobal.value.typesSettings![i].name,
                            active: false,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      child: Column(
                        children: [
                          AddServiceFormField(
                            initialValue: controller
                                .howManyCloth[services[i].typeModel?.id]
                                .toString(),
                            keyboardType: TextInputType.number,
                            onChanged: (value) =>
                                controller.onChangedHowManyCloth(
                                    int.tryParse(value) ?? 0,
                                    services[i].typeModel?.id ?? 0),
                            active: true,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
          ],
        ),
      );
    });
  }
}
