import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/provider_page_filtered_controller.dart';
import 'package:get_clean/global/constants/theme.dart';

import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/models/car_model.dart';
import '../../../add_new_service/views/widgets/add_service_form_field.dart';

class CarServiceWidget extends GetView<ProviderPageFilteredController> {
  const CarServiceWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final carService = controller.provider.services
        ?.firstWhereOrNull((element) => element.pricingOption?.id == 4);

    log('safsafsaf ${carService?.pricingList}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
            Get.find<LanguageController>().keys.value.carServices ??
                'Car Services',
            style: bigTextStyle),
        if (carService?.pricingList?.isNotEmpty ?? false)
          StatefulBuilder(builder: (context, setState) {
            return ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: carService?.pricingList?.length,
              itemBuilder: (context, index) {
                final car = carService?.pricingList?[index].typeModel ??
                    CarModel.empty();

                // final isCarSelectedByType = controller
                //     .selectedCarService.value.carType
                //     .contains(car.carType);

                void onChangedCar(value, i) {
                  controller.onChangeSelectedCarService(car.carServices[i],
                      mainCarModel: car);

                  setState(() {});
                }

                //? remove all parents with none price
                final carServices = car.carServices
                    .where((element) => element.price.text.isNotEmpty)
                    .toList();

                if (carServices.isEmpty) return const SizedBox.shrink();

                return Container(
                  margin: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: const Color(0xffF3F3F3),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            car.name.text,
                            style: big2TextStyle,
                          ),
                          const SizedBox(width: 10),
                          if (car.carServices
                              .contains(controller.selectedCarService.value))
                            SizedBox(
                              height: 50,
                              width: 110,
                              child: AddServiceFormField(
                                keyboardType: TextInputType.number,
                                controller: controller.howManyCars,
                                onChanged: (value) {
                                  if (value == '' || value == '0') return;
                                  controller.requestPrice();
                                },
                                active: true,
                              ),
                            ),
                        ],
                      ).paddingSymmetric(
                        horizontal: 10,
                      ),
                      SizedBox(
                        height: 55,
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          children: [
                            for (int i = 0; i < car.carServices.length; i++)
                              if (car.carServices[i].price.text.isNotEmpty &&
                                  car.carServices[i].price.text != 'null')
                                SizedBox(
                                  width: 130,
                                  child: Builder(builder: (context) {
                                    //? select first car by default
                                    // if (controller.selectedCarService.value ==
                                    //     CarModel.empty()) {
                                    //   // log('asfasfsa $i');
                                    //
                                    //
                                    //   onChangedCar(car.carServices[i], i);
                                    // }

                                    return RadioListTile(
                                        value: car.carServices[i],
                                        groupValue:
                                            controller.selectedCarService.value,
                                        title: Text(
                                          '${car.carServices[i].name.text} - ${car.carServices[i].price.text} \$',
                                          style: middleGreyTextStyle,
                                        ),
                                        onChanged: (value) =>
                                            onChangedCar(value, i));
                                  }),
                                ),
                          ],
                        ),
                      ),
                    ],
                  ).paddingAll(8),
                );
              },
            );
          }),
      ],
    );
  }
}

// StatefulBuilder(builder: (context, setState) {
// return ListView.builder(
// shrinkWrap: true,
// physics: const NeverScrollableScrollPhysics(),
// itemCount: carService?.pricingList?.length,
// itemBuilder: (context, index) {
// final car =
// carService?.pricingList?[index].carModel ?? CarModel.empty();
//
// void onChangedCar(value, i) {
// controller.onChangeSelectedCarService(car.carServices[i]);
//
// setState(() {});
// }
//
// return Container(
// margin: const EdgeInsets.all(10),
// decoration: BoxDecoration(
// borderRadius: BorderRadius.circular(15),
// color: const Color(0xffF3F3F3),
// ),
// child: ExpansionTile(
// initiallyExpanded: index == 0 ? true : false,
// shape: RoundedRectangleBorder(
// borderRadius: BorderRadius.circular(15),
// ),
// title: Text(car.name.text),
// childrenPadding: const EdgeInsets.all(8),
// children: [
// Row(
// children: [
// //! Car Type ================================
// Expanded(
// child: Column(
// children: [
// TextWithBackground(
// color: primaryColor,
// text: Get.find<LanguageController>()
//     .keys
//     .value
//     .carType!,
// ),
// for (int i = 0; i < car.carServices.length; i++)
// Builder(builder: (context) {
// return Row(
// children: [
// Radio(
// value: car.carServices[i],
// groupValue: controller
//     .selectedCarService.value,
// onChanged: (value) =>
// onChangedCar(value, i)),
// Expanded(
// child: AddServiceFormField(
// keyboardType: TextInputType.text,
// controller: car.carServices[i].name,
// active: false,
// ),
// ),
// ],
// );
// }),
// ],
// ),
// ),
//
// SizedBox(width: 10.w),
//
// //! Price ================================
// Expanded(
// child: Column(
// children: [
// Row(
// children: [
// Expanded(
// child: TextWithBackground(
// color: primaryColor,
// text: Get.find<LanguageController>()
//     .keys
//     .value
//     .price!,
// ),
// ),
// ],
// ),
// for (int i = 0; i < car.carServices.length; i++)
// Row(
// children: [
// Expanded(
// child: AddServiceFormField(
// keyboardType: TextInputType.number,
// controller: car.carServices[i].price,
// active: true,
// ),
// ),
// ],
// ),
// ],
// ),
// ),
// ],
// ),
// ],
// ),
// );
// },
// );
// }),
