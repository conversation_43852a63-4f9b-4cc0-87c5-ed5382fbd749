import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/provider_page_filtered_controller.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/states/provider_page_filtered_states.dart';

import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';

class PriceSection extends StatelessWidget {
  const PriceSection({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProviderPageFilteredController>(builder: (controller) {
      if (controller.state.value is ProviderPageFilteredPriceLoadingState) {
        return Container(
          color: Colors.white,
          child: Container(
            height: 70.h,
            width: Get.width,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey[400]!,
                  blurRadius: 2,
                ),
              ],
            ),
            child: Center(
              child: Text(
                Get.find<LanguageController>().keys.value.calculatePrice!,
                style: regularTextStyle,
              ),
            ),
          ),
        );
      }
      return Container(
        color: Colors.white,
        child: Container(
          height: 70.h,
          width: Get.width,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey[400]!,
                blurRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    Get.find<LanguageController>().keys.value.discountAmount!,
                    style: regularTextStyle,
                  ),
                  Text(
                    '${controller.price.value.data?.discountAmount?.toStringAsFixed(2) ?? 0} ${Get.find<LanguageController>().keys.value.ils!}',
                    style: regularTextStyle,
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    Get.find<LanguageController>().keys.value.total!,
                    style: regularTextStyle,
                  ),
                  Text(
                    '${controller.price.value.data?.totalPrice?.toStringAsFixed(2) ?? 0} ${Get.find<LanguageController>().keys.value.ils!}',
                    style: regularTextStyle,
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${Get.find<LanguageController>().keys.value.deposit!} (${controller.price.value.data?.depositPercentage ?? 0}%)',
                    style: smallGreyTextStyle,
                  ),
                  Text(
                    '${controller.price.value.data?.deposit ?? 0} ${Get.find<LanguageController>().keys.value.ils!}',
                    style: smallGreyTextStyle,
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }
}
