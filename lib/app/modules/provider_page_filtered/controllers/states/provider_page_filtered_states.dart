import 'package:get_clean/global/models/booking_response.dart';
import 'package:get_clean/global/models/price_model.dart';
import 'package:get_clean/global/models/provider_avilable_times.dart';

class ProviderPageFilteredState {
  String? errorMessage;
}

class ProviderPageFilteredSuccessState extends ProviderPageFilteredState {}

class ProviderPageFilteredFailedState extends ProviderPageFilteredState {
  ProviderPageFilteredFailedState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class ProviderPageFilteredLoadingState extends ProviderPageFilteredState {}

class ProviderPageFilteredTimesLoadingState extends ProviderPageFilteredState {}

class ProviderPageFilteredTimesState extends ProviderPageFilteredState {
  ProviderAvilableTimes? times;
  ProviderPageFilteredTimesState(ProviderAvilableTimes this.times);
}

class ProviderPageFilteredPriceLoadingState extends ProviderPageFilteredState {}

class ProviderPageFilteredPriceSuccessState extends ProviderPageFilteredState {
  BookingResponse? booking;
  ProviderPageFilteredPriceSuccessState(this.booking);
}

class ProviderPageFilteredPriceState extends ProviderPageFilteredState {
  PriceModel? price;
  ProviderPageFilteredPriceState(PriceModel this.price);
}
