import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/favorites_model.dart';

class FavoriteRemoteProvider {
  DioHelper helper = DioHelper();

  //? Get Favorite
  Future<AllFavoritesModel> getFavorite() async {
    try {
      final response = await helper.getData(getFavoritesURL);
      if (response['success'] == true) {
        return AllFavoritesModel.fromJson(response);
      } else {
        showErrorToast(response['message']);
        return AllFavoritesModel(
          favoriteProviders: RxList([]),
        );
      }
    } catch (e) {
      return AllFavoritesModel(
        favoriteProviders: RxList([]),
      );
    }
  }

  //? add new favorite
  Future<bool> addNewFavorite({
    required int? providerId,
  }) async {
    try {
      final response = await helper.postData(
        addNewFavoriteURL,
        {
          "provider_id": providerId,
        },
      );

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}
