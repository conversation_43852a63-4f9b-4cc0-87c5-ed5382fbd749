import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/jobs/models/calendar_job_model.dart';
import 'package:get_clean/app/modules/jobs/models/job_application_model.dart';
import 'package:get_clean/app/modules/jobs/models/job_model.dart';
import 'package:get_clean/app/modules/jobs/provider/jobs_remote_provider.dart';
import 'package:get_clean/app/modules/my_profile/provider/my_profile_remote_provider.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:intl/intl.dart';

import '../../../../global/models/provider_services.dart';
import '../../../../global/models/work_location_model.dart';

class JobsController extends GetxController {
  // Map to track which days are expanded/visible
  final expandedDays = <String, RxBool>{
    'monday': false.obs,
    'tuesday': false.obs,
    'wednesday': false.obs,
    'thursday': false.obs,
    'friday': false.obs,
    'saturday': false.obs,
    'sunday': false.obs,
  };
  final jobsProvider = JobsRemoteProvider();
  final myProfileProvider = MyProfileRemoteProvider();
  final globalValuesController = Get.find<GlobalValuesController>();

  // Jobs list
  final jobs = <Job>[].obs;
  final isLoading = false.obs;

  // Job Applications list
  final jobApplications = <JobApplication>[].obs;
  final isLoadingApplications = false.obs;

  // Calendar Jobs
  final calendarJobs = <CalendarJob>[].obs;
  final isLoadingCalendar = false.obs;
  final selectedMonth = DateTime.now().obs;
  final selectedDate = [DateTime.now()].obs;
  final selectedDayJobs = <CalendarJob>[].obs;

  // Selected job for editing
  final selectedJob = Rxn<Job>();

  // Form controllers
  final notesController = TextEditingController();

  // Day controllers for schedule
  final mondayStartTimeController = TextEditingController();
  final mondayDurationController = TextEditingController();

  final tuesdayStartTimeController = TextEditingController();
  final tuesdayDurationController = TextEditingController();

  final wednesdayStartTimeController = TextEditingController();
  final wednesdayDurationController = TextEditingController();

  final thursdayStartTimeController = TextEditingController();
  final thursdayDurationController = TextEditingController();

  final fridayStartTimeController = TextEditingController();
  final fridayDurationController = TextEditingController();

  final saturdayStartTimeController = TextEditingController();
  final saturdayDurationController = TextEditingController();

  final sundayStartTimeController = TextEditingController();
  final sundayDurationController = TextEditingController();

  // Dropdown values
  final selectedService = Rxn<ProviderServices>();
  final selectedLocation = Rxn<WorkLocation>();

  // Data for dropdowns
  final services = <ProviderServices>[].obs;
  final locations = <WorkLocation>[].obs;

  @override
  void onInit() {
    super.onInit();
    fetchJobs();
    fetchJobApplications();
    fetchServices();
    fetchLocations();
    fetchCalendarJobs();
    updateSelectedDateJobs();
  }

  @override
  void onClose() {
    notesController.dispose();

    // Dispose day controllers
    mondayStartTimeController.dispose();
    mondayDurationController.dispose();

    tuesdayStartTimeController.dispose();
    tuesdayDurationController.dispose();

    wednesdayStartTimeController.dispose();
    wednesdayDurationController.dispose();

    thursdayStartTimeController.dispose();
    thursdayDurationController.dispose();

    fridayStartTimeController.dispose();
    fridayDurationController.dispose();

    saturdayStartTimeController.dispose();
    saturdayDurationController.dispose();

    sundayStartTimeController.dispose();
    sundayDurationController.dispose();

    super.onClose();
  }

  // Fetch jobs from API
  Future<void> fetchJobs() async {
    isLoading.value = true;
    update();

    try {
      final response = await jobsProvider.getJobs();

      log('asfafasfa223232s ${response}');

      if (response != null) {
        jobs.value = response.data;
        log('asfafasfas ${jobs.value}');
      }
    } catch (e) {
      log('Error fetching jobs: ${e.toString()}');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Fetch job applications from API
  Future<void> fetchJobApplications() async {
    isLoadingApplications.value = true;
    update();

    try {
      final response = await jobsProvider.getJobApplications();

      if (response != null) {
        jobApplications.value = response.data;
        log('Job Applications: ${jobApplications.length}');
      }
    } catch (e) {
      log('Error fetching job applications: ${e.toString()}');
    } finally {
      isLoadingApplications.value = false;
      update();
    }
  }

  // Fetch calendar jobs from API
  Future<void> fetchCalendarJobs() async {
    isLoadingCalendar.value = true;
    update();

    try {
      // For now, using a dummy provider ID. You should get this from user data
      final providerId = 11; // Replace with actual provider ID
      final monthString =
          "${selectedMonth.value.year}-${selectedMonth.value.month.toString().padLeft(2, '0')}";

      final response = await jobsProvider.getCalendarJobs(
        providerId: providerId,
        day: selectedDate.first.day.toString(),
        month: monthString,
      );

      if (response != null) {
        calendarJobs.value = response.data;
        updateSelectedDateJobs();
        log('Calendar Jobs: ${calendarJobs.length}');
      }
    } catch (e) {
      log('Error fetching calendar jobs: ${e.toString()}');
    } finally {
      isLoadingCalendar.value = false;
      update();
    }
  }

  // Fetch services for dropdown
  Future<void> fetchServices() async {
    try {
      final servicesList = await globalValuesController.getAllServices();
      services.value = servicesList;
      update();
    } catch (e) {
      log('Error fetching services: ${e.toString()}');
    }
  }

  // Fetch work locations for dropdown
  Future<void> fetchLocations() async {
    try {
      final response = await myProfileProvider.getWorkLocations();
      if (response != null) {
        locations.value = response.data;
      }
      update();
    } catch (e) {
      log('Error fetching locations: ${e.toString()}');
    }
  }

  // Add new job
  Future<bool> addJob() async {
    if (!validateForm()) {
      return false;
    }

    final data = {
      'service_id': selectedService.value!.id,
      'location_id': selectedLocation.value?.id,
      'notes': notesController.text,
    };

    // Add schedule data for each day if time is set
    if (mondayStartTimeController.text.isNotEmpty &&
        mondayDurationController.text.isNotEmpty) {
      data['monday_schedule[0][start_time]'] = mondayStartTimeController.text;
      data['monday_schedule[0][duration]'] = mondayDurationController.text;
    }

    if (tuesdayStartTimeController.text.isNotEmpty &&
        tuesdayDurationController.text.isNotEmpty) {
      data['tuesday_schedule[0][start_time]'] = tuesdayStartTimeController.text;
      data['tuesday_schedule[0][duration]'] = tuesdayDurationController.text;
    }

    if (wednesdayStartTimeController.text.isNotEmpty &&
        wednesdayDurationController.text.isNotEmpty) {
      data['wednesday_schedule[0][start_time]'] =
          wednesdayStartTimeController.text;
      data['wednesday_schedule[0][duration]'] =
          wednesdayDurationController.text;
    }

    if (thursdayStartTimeController.text.isNotEmpty &&
        thursdayDurationController.text.isNotEmpty) {
      data['thursday_schedule[0][start_time]'] =
          thursdayStartTimeController.text;
      data['thursday_schedule[0][duration]'] = thursdayDurationController.text;
    }

    if (fridayStartTimeController.text.isNotEmpty &&
        fridayDurationController.text.isNotEmpty) {
      data['friday_schedule[0][start_time]'] = fridayStartTimeController.text;
      data['friday_schedule[0][duration]'] = fridayDurationController.text;
    }

    if (saturdayStartTimeController.text.isNotEmpty &&
        saturdayDurationController.text.isNotEmpty) {
      data['saturday_schedule[0][start_time]'] =
          saturdayStartTimeController.text;
      data['saturday_schedule[0][duration]'] = saturdayDurationController.text;
    }

    if (sundayStartTimeController.text.isNotEmpty &&
        sundayDurationController.text.isNotEmpty) {
      data['sunday_schedule[0][start_time]'] = sundayStartTimeController.text;
      data['sunday_schedule[0][duration]'] = sundayDurationController.text;
    }

    final result = await jobsProvider.addJob(data);
    if (result) {
      resetForm();
      await fetchJobs();
    }

    return result;
  }

  // Delete job
  Future<bool> deleteJob(int id) async {
    final result = await jobsProvider.deleteJob(id);
    if (result) {
      await fetchJobs();
    }
    return result;
  }

  // Select job for editing
  void selectJob(Job job) {
    selectedJob.value = job;

    // Set form values
    notesController.text = job.notes ?? '';

    // Find and set service
    if (job.service != null) {
      selectedService.value =
          services.firstWhereOrNull((s) => s.id == job.service!.id);
    }

    // Find and set location
    if (job.location != null) {
      selectedLocation.value =
          locations.firstWhereOrNull((l) => l.id == job.location!.id);
    }

    // Reset all schedule controllers first
    mondayStartTimeController.clear();
    mondayDurationController.clear();
    tuesdayStartTimeController.clear();
    tuesdayDurationController.clear();
    wednesdayStartTimeController.clear();
    wednesdayDurationController.clear();
    thursdayStartTimeController.clear();
    thursdayDurationController.clear();
    fridayStartTimeController.clear();
    fridayDurationController.clear();
    saturdayStartTimeController.clear();
    saturdayDurationController.clear();
    sundayStartTimeController.clear();
    sundayDurationController.clear();

    // Set schedule if available
    if (job.schedule != null && job.schedule!.isNotEmpty) {
      for (final schedule in job.schedule!) {
        if (schedule.hours != null && schedule.hours!.isNotEmpty) {
          final hours = schedule.hours![0];

          final day = schedule.day?.toLowerCase() ?? '';

          if (day == 'monday') {
            mondayStartTimeController.text = hours.startTime ?? '';
            mondayDurationController.text = hours.duration ?? '';
          } else if (day == 'tuesday') {
            tuesdayStartTimeController.text = hours.startTime ?? '';
            tuesdayDurationController.text = hours.duration ?? '';
          } else if (day == 'wednesday') {
            wednesdayStartTimeController.text = hours.startTime ?? '';
            wednesdayDurationController.text = hours.duration ?? '';
          } else if (day == 'thursday') {
            thursdayStartTimeController.text = hours.startTime ?? '';
            thursdayDurationController.text = hours.duration ?? '';
          } else if (day == 'friday') {
            fridayStartTimeController.text = hours.startTime ?? '';
            fridayDurationController.text = hours.duration ?? '';
          } else if (day == 'saturday') {
            saturdayStartTimeController.text = hours.startTime ?? '';
            saturdayDurationController.text = hours.duration ?? '';
          } else if (day == 'sunday') {
            sundayStartTimeController.text = hours.startTime ?? '';
            sundayDurationController.text = hours.duration ?? '';
          }
        }
      }
    }

    update();
  }

  // Reset form
  // Toggle day visibility
  void toggleDayVisibility(String day) {
    expandedDays[day]?.value = !(expandedDays[day]?.value ?? false);
    update();
  }

  // Calendar methods
  void onMonthChanged(DateTime month) {
    selectedMonth.value = month;
    //set date with first day of month
    selectedDate.value = [
      DateTime(month.year, month.month, 1),
    ];
    fetchCalendarJobs();
  }

  void onDateSelected(DateTime date) {
    selectedDate.value = [date];
    updateSelectedDateJobs();
    fetchCalendarJobs();
  }

  final selectedDateJobs = <CalendarJob>[].obs;

  void updateSelectedDateJobs() {
    final dateString = DateFormat('yyyy-MM-dd').format(selectedDate.first);
    selectedDateJobs.value = calendarJobs.where((job) {
      final jobDateString = DateFormat('yyyy-MM-dd').format(job.parsedDate);
      return jobDateString == dateString;
    }).toList();
    update();
  }

  // Get jobs count for a specific date
  int getJobsCountForDate(DateTime date) {
    final dateString = DateFormat('yyyy-MM-dd').format(date);
    return calendarJobs.where((job) {
      final jobDateString = DateFormat('yyyy-MM-dd').format(job.parsedDate);
      return jobDateString == dateString;
    }).length;
  }

  // Update task time with location validation
  Future<void> updateTaskTime({
    required CalendarJob job,
    String? endTime,
  }) async {
    if (job.jobApplicationId == null || job.dId == null) {
      showErrorToast('Invalid job data');
    }

    isLoadingCalendar.value = true;
    update();
    // Check location validation for job applications
    if (job.type == 'job_application') {
      final isWithinRange = await _validateLocation(job);
      isLoadingCalendar.value = false;
      update();
      if (!isWithinRange) {
        showErrorToast(
            Get.find<LanguageController>().keys.value.notWithinWorkLocation ??
                'You are not within 100m of the work location');
        return;
      }
    }

    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Text(
            Get.find<LanguageController>().keys.value.warning ?? 'Warning'),
        content: Text(
          Get.find<LanguageController>()
                  .keys
                  .value
                  .areYouSureYouWantToMakeThisAction ??
              'Are you sure you want to make this action?',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              Get.find<LanguageController>().keys.value.cancel ?? 'Cancel',
              style: const TextStyle(color: Colors.black),
            ),
          ),
          TextButton(
            onPressed: () async {
              final now = DateTime.now();
              final amOrPm = now.hour >= 12 ? 'PM' : 'AM';
              final timeAs12Format = now.hour % 12;
              final timeString =
                  '$timeAs12Format:${now.minute.toString().padLeft(2, '0')} $amOrPm';
              final executionDate =
                  '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

              final result = await jobsProvider.updateTaskTime(
                jobApplicationId: job.jobApplicationId!,
                dId: job.dId!,
                executionDate: executionDate,
                time: timeString,
                endTime: endTime,
              );

              if (result) {
                Get.back();
                fetchCalendarJobs();
              }
            },
            child: Text(
              Get.find<LanguageController>().keys.value.save ?? 'Save',
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  // Validate user location against job location
  Future<bool> _validateLocation(CalendarJob job) async {
    try {
      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          showErrorToast('Location permission denied');
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        showErrorToast('Location permission permanently denied');
        return false;
      }

      // Get current position
      Position currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get job location coordinates
      final jobLat = job.latitude;
      final jobLng = job.longitude;

      log('Job_Lat_And_Lng: $jobLat, $jobLng');
      log('Current Position: ${currentPosition.latitude}, ${currentPosition.longitude}');

      if (jobLat == null || jobLng == null) {
        showErrorToast('Job location coordinates not available');
        return false;
      }

      // Calculate distance
      double distanceInMeters = Geolocator.distanceBetween(
        currentPosition.latitude,
        currentPosition.longitude,
        jobLat,
        jobLng,
      );

      log('Distance to job location: ${distanceInMeters.toStringAsFixed(2)} meters');

      // Check if within 100 meters
      return distanceInMeters <= 100;
    } catch (e) {
      log('Error validating location: ${e.toString()}');
      showErrorToast('Error getting location');
      return false;
    }
  }

  void resetForm() {
    selectedJob.value = null;
    selectedService.value = null;
    selectedLocation.value = null;
    notesController.clear();

    // Clear all schedule controllers
    mondayStartTimeController.clear();
    mondayDurationController.clear();
    tuesdayStartTimeController.clear();
    tuesdayDurationController.clear();
    wednesdayStartTimeController.clear();
    wednesdayDurationController.clear();
    thursdayStartTimeController.clear();
    thursdayDurationController.clear();
    fridayStartTimeController.clear();
    fridayDurationController.clear();
    saturdayStartTimeController.clear();
    saturdayDurationController.clear();
    sundayStartTimeController.clear();
    sundayDurationController.clear();

    // Reset expanded days
    expandedDays.forEach((day, value) {
      value.value = false;
    });

    update();
  }

  // Validate form
  bool validateForm() {
    if (selectedService.value == null) {
      showErrorToast(Get.find<LanguageController>().keys.value.selectService ??
          'Please select a service');
      return false;
    }

    if (selectedLocation.value == null) {
      showErrorToast(Get.find<LanguageController>().keys.value.selectLocation ??
          'Please select a location');
      return false;
    }

    // Check if at least one day has a schedule
    bool hasSchedule = false;

    final langController = Get.find<LanguageController>();

    if (mondayStartTimeController.text.isNotEmpty ||
        mondayDurationController.text.isNotEmpty) {
      if (mondayStartTimeController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.monday ?? 'Monday'} ${langController.keys.value.startTime ?? 'start time'}');
        return false;
      }
      if (mondayDurationController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.monday ?? 'Monday'} ${langController.keys.value.duration ?? 'duration'}');
        return false;
      }
      hasSchedule = true;
    }

    if (tuesdayStartTimeController.text.isNotEmpty ||
        tuesdayDurationController.text.isNotEmpty) {
      if (tuesdayStartTimeController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.tuesday ?? 'Tuesday'} ${langController.keys.value.startTime ?? 'start time'}');
        return false;
      }
      if (tuesdayDurationController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.tuesday ?? 'Tuesday'} ${langController.keys.value.duration ?? 'duration'}');
        return false;
      }
      hasSchedule = true;
    }

    if (wednesdayStartTimeController.text.isNotEmpty ||
        wednesdayDurationController.text.isNotEmpty) {
      if (wednesdayStartTimeController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.wednesday ?? 'Wednesday'} ${langController.keys.value.startTime ?? 'start time'}');
        return false;
      }
      if (wednesdayDurationController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.wednesday ?? 'Wednesday'} ${langController.keys.value.duration ?? 'duration'}');
        return false;
      }
      hasSchedule = true;
    }

    if (thursdayStartTimeController.text.isNotEmpty ||
        thursdayDurationController.text.isNotEmpty) {
      if (thursdayStartTimeController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.thursday ?? 'Thursday'} ${langController.keys.value.startTime ?? 'start time'}');
        return false;
      }
      if (thursdayDurationController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.thursday ?? 'Thursday'} ${langController.keys.value.duration ?? 'duration'}');
        return false;
      }
      hasSchedule = true;
    }

    if (fridayStartTimeController.text.isNotEmpty ||
        fridayDurationController.text.isNotEmpty) {
      if (fridayStartTimeController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.friday ?? 'Friday'} ${langController.keys.value.startTime ?? 'start time'}');
        return false;
      }
      if (fridayDurationController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.friday ?? 'Friday'} ${langController.keys.value.duration ?? 'duration'}');
        return false;
      }
      hasSchedule = true;
    }

    if (saturdayStartTimeController.text.isNotEmpty ||
        saturdayDurationController.text.isNotEmpty) {
      if (saturdayStartTimeController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.saturday ?? 'Saturday'} ${langController.keys.value.startTime ?? 'start time'}');
        return false;
      }
      if (saturdayDurationController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.saturday ?? 'Saturday'} ${langController.keys.value.duration ?? 'duration'}');
        return false;
      }
      hasSchedule = true;
    }

    if (sundayStartTimeController.text.isNotEmpty ||
        sundayDurationController.text.isNotEmpty) {
      if (sundayStartTimeController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.sunday ?? 'Sunday'} ${langController.keys.value.startTime ?? 'start time'}');
        return false;
      }
      if (sundayDurationController.text.isEmpty) {
        showErrorToast(
            '${langController.keys.value.pleaseEnter ?? 'Please enter'} ${langController.keys.value.sunday ?? 'Sunday'} ${langController.keys.value.duration ?? 'duration'}');
        return false;
      }
      hasSchedule = true;
    }

    if (!hasSchedule) {
      showErrorToast(langController.keys.value.pleaseSetSchedule ??
          'Please set schedule for at least one day');
      return false;
    }

    return true;
  }

  // Service selection handler
  void onServiceChanged(ProviderServices? service) {
    selectedService.value = service;
    update();
  }

  // Location selection handler
  void onLocationChanged(WorkLocation? location) {
    selectedLocation.value = location;
    update();
  }
}
