import 'package:get/get.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/models/provider_services.dart';

import '../../../../global/models/work_location_model.dart';

class JobsResponse {
  final String status;
  final List<Job> data;

  JobsResponse({
    required this.status,
    required this.data,
  });

  factory JobsResponse.fromJson(Map<String, dynamic> json) {
    return JobsResponse(
      status: json['status'] ?? '',
      data: json['data'] != null
          ? List<Job>.from(json['data'].map((x) => Job.fromJson(x)))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'data': data.map((x) => x.toJson()).toList(),
    };
  }
}

class Job {
  final int? id;
  final User? user;
  final ProviderServices? service;
  final WorkLocation? location;
  final List<Schedule>? schedule;
  final String? status;
  final String? notes;
  final String? adminNotes;
  final String? approvedAt;
  final String? approvedBy;
  final String? createdAt;
  final String? updatedAt;

  Job({
    this.id,
    this.user,
    this.service,
    this.location,
    this.schedule,
    this.status,
    this.notes,
    this.adminNotes,
    this.approvedAt,
    this.approvedBy,
    this.createdAt,
    this.updatedAt,
  });

  factory Job.fromJson(Map<String, dynamic> json) {
    return Job(
      id: json['id'],
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      service: json['service'] != null
          ? ProviderServices.fromJson(json['service'])
          : null,
      location: json['location'] != null
          ? WorkLocation.fromJson(json['location'])
          : null,
      schedule: json['schedule'] != null
          ? List<Schedule>.from(
              json['schedule'].map((x) => Schedule.fromJson(x)))
          : null,
      status: json['status'],
      notes: json['notes'],
      adminNotes: json['admin_notes'],
      approvedAt: json['approved_at'],
      approvedBy: json['approved_by'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': user?.toJson(),
      'service': service?.toJson(),
      'location': location?.toJson(),
      'schedule': schedule?.map((x) => x.toJson()).toList(),
      'status': status,
      'notes': notes,
      'admin_notes': adminNotes,
      'approved_at': approvedAt,
      'approved_by': approvedBy,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

class User {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? gender;
  final String? image;

  User({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.gender,
    this.image,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      gender: json['gender'],
      image: json['image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'gender': gender,
      'image': image,
    };
  }
}

class Service {
  final int? id;
  final Map<String, String>? name;
  final int? ranking;
  final int? categoryId;
  final int? isActive;
  final int? isHome;
  final String? image;
  final String? deletedAt;
  final String? createdAt;
  final String? updatedAt;

  Service({
    this.id,
    this.name,
    this.ranking,
    this.categoryId,
    this.isActive,
    this.isHome,
    this.image,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'],
      name:
          json['name'] != null ? Map<String, String>.from(json['name']) : null,
      ranking: json['ranking'],
      categoryId: json['category_id'],
      isActive: json['is_active'],
      isHome: json['is_home'],
      image: json['image'],
      deletedAt: json['deleted_at'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'ranking': ranking,
      'category_id': categoryId,
      'is_active': isActive,
      'is_home': isHome,
      'image': image,
      'deleted_at': deletedAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  String get displayName {
    final currentLanguage =
        Get.find<LanguageController>().selectedLanguage.value.slug;
    return name?[currentLanguage] ?? name?['en'] ?? '';
  }
}

class Schedule {
  final String? day;
  final List<Hours>? hours;

  Schedule({
    this.day,
    this.hours,
  });

  factory Schedule.fromJson(Map<String, dynamic> json) {
    return Schedule(
      day: json['day'],
      hours: json['hours'] != null
          ? List<Hours>.from(json['hours'].map((x) => Hours.fromJson(x)))
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'hours': hours?.map((x) => x.toJson()).toList(),
    };
  }
}

class Hours {
  final String? startTime;
  final String? duration;
  final String? endTime;

  Hours({
    this.startTime,
    this.duration,
    this.endTime,
  });

  factory Hours.fromJson(Map<String, dynamic> json) {
    return Hours(
      startTime: json['start_time'],
      duration: json['duration'],
      endTime: json['end_time'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'start_time': startTime,
      'duration': duration,
      'end_time': endTime,
    };
  }
}
