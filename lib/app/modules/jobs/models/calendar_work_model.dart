class CalendarWorksResponse {
  final bool success;
  final String month;
  final List<CalendarWork> data;

  CalendarWorksResponse({
    required this.success,
    required this.month,
    required this.data,
  });

  factory CalendarWorksResponse.fromJson(Map<String, dynamic> json) {
    return CalendarWorksResponse(
      success: json['success'] ?? false,
      month: json['month'] ?? '',
      data: json['data'] != null
          ? List<CalendarWork>.from(
              json['data'].map((x) => CalendarWork.fromJson(x)))
          : [],
    );
  }
}

class CalendarWork {
  final String type; // "order" or "job_application"
  final int? orderId;
  final int? jobApplicationId;
  final int? dId;
  final String date;
  final String day;
  final String startTime;
  final String endTime;
  final String status;
  final WorkTask? task;

  CalendarWork({
    required this.type,
    this.orderId,
    this.jobApplicationId,
    this.dId,
    required this.date,
    required this.day,
    required this.startTime,
    required this.endTime,
    required this.status,
    this.task,
  });

  factory CalendarWork.fromJson(Map<String, dynamic> json) {
    return CalendarWork(
      type: json['type'] ?? '',
      orderId: json['order_id'],
      jobApplicationId: json['job_application_id'],
      dId: json['d_id'],
      date: json['date'] ?? '',
      day: json['day'] ?? '',
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
      status: json['status'] ?? '',
      task: json['task'] != null ? WorkTask.fromJson(json['task']) : null,
    );
  }

  // Helper method to get status color
  String get statusColor {
    switch (status.toLowerCase()) {
      case 'approved':
        return '#4CAF50'; // Green
      case 'absent':
        return '#F44336'; // Red
      case 'not_started':
        return '#FF9800'; // Orange
      case 'ended':
        return '#2196F3'; // Blue
      default:
        return '#9E9E9E'; // Grey
    }
  }

  // Helper method to check if work can be started
  bool get canStart => task == null && status.toLowerCase() != 'ended';

  // Helper method to check if work can be ended
  bool get canEnd => task != null && status.toLowerCase() != 'ended';
}

class WorkTask {
  final int id;
  final String startTime;
  final String endTime;

  WorkTask({
    required this.id,
    required this.startTime,
    required this.endTime,
  });

  factory WorkTask.fromJson(Map<String, dynamic> json) {
    return WorkTask(
      id: json['id'] ?? 0,
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
    );
  }
}

// Enum for work status
enum WorkStatus {
  approved,
  absent,
  notStarted,
  ended;

  static WorkStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return WorkStatus.approved;
      case 'absent':
        return WorkStatus.absent;
      case 'not_started':
        return WorkStatus.notStarted;
      case 'ended':
        return WorkStatus.ended;
      default:
        return WorkStatus.approved;
    }
  }

  String get displayName {
    switch (this) {
      case WorkStatus.approved:
        return 'Approved';
      case WorkStatus.absent:
        return 'Absent';
      case WorkStatus.notStarted:
        return 'Not Started';
      case WorkStatus.ended:
        return 'Ended';
    }
  }
}
