import 'package:get_clean/app/modules/jobs/models/job_model.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_clean/global/models/work_location_model.dart';

class JobApplicationsResponse {
  final bool success;
  final String message;
  final List<JobApplication> data;

  JobApplicationsResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory JobApplicationsResponse.fromJson(Map<String, dynamic> json) {
    return JobApplicationsResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null
          ? List<JobApplication>.from(
              json['data'].map((x) => JobApplication.fromJson(x)))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data.map((x) => x.toJson()).toList(),
    };
  }
}

class JobApplication {
  final int? id;
  final User? user;
  final Provider? provider;
  final ProviderServices? service;
  final WorkLocation? location;
  final List<Schedule>? schedule;
  final String? calculationType;
  final num? fixedAmount;
  final String? providerPaymentMethod;
  final num? providerHourlyCost;
  final num? providerFixedCost;
  final num? systemHourlyCost;
  final num? systemTransportationCost;
  final num? providerTransportationCost;
  final bool? includeTransportation;
  final String? status;
  final String? notes;
  final bool? isActive;
  final bool? isRecurring;
  final String? endDate;
  final String? cancellationReason;
  final String? stopDate;
  final String? stopReason;

  JobApplication({
    this.id,
    this.user,
    this.provider,
    this.service,
    this.location,
    this.schedule,
    this.calculationType,
    this.fixedAmount,
    this.providerPaymentMethod,
    this.providerHourlyCost,
    this.providerFixedCost,
    this.systemHourlyCost,
    this.systemTransportationCost,
    this.providerTransportationCost,
    this.includeTransportation,
    this.status,
    this.notes,
    this.isActive,
    this.isRecurring,
    this.endDate,
    this.cancellationReason,
    this.stopDate,
    this.stopReason,
  });

  factory JobApplication.fromJson(Map<String, dynamic> json) {
    return JobApplication(
      id: json['id'],
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      provider: json['provider'] != null ? Provider.fromJson(json['provider']) : null,
      service: json['service'] != null
          ? ProviderServices.fromJson(json['service'])
          : null,
      location: json['location'] != null
          ? WorkLocation.fromJson(json['location'])
          : null,
      schedule: json['schedule'] != null
          ? List<Schedule>.from(
              json['schedule'].map((x) => Schedule.fromJson(x)))
          : null,
      calculationType: json['calculation_type'],
      fixedAmount: json['fixed_amount'],
      providerPaymentMethod: json['provider_payment_method'],
      providerHourlyCost: json['provider_hourly_cost'],
      providerFixedCost: json['provider_fixed_cost'],
      systemHourlyCost: json['system_hourly_cost'],
      systemTransportationCost: json['system_transportation_cost'],
      providerTransportationCost: json['provider_transportation_cost'],
      includeTransportation: json['include_transportation'],
      status: json['status'],
      notes: json['notes'],
      isActive: json['is_active'],
      isRecurring: json['is_recurring'],
      endDate: json['end_date'],
      cancellationReason: json['cancellation_reason'],
      stopDate: json['stop_date'],
      stopReason: json['stop_reason'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': user?.toJson(),
      'provider': provider?.toJson(),
      'service': service?.toJson(),
      'location': location?.toJson(),
      'schedule': schedule?.map((x) => x.toJson()).toList(),
      'calculation_type': calculationType,
      'fixed_amount': fixedAmount,
      'provider_payment_method': providerPaymentMethod,
      'provider_hourly_cost': providerHourlyCost,
      'provider_fixed_cost': providerFixedCost,
      'system_hourly_cost': systemHourlyCost,
      'system_transportation_cost': systemTransportationCost,
      'provider_transportation_cost': providerTransportationCost,
      'include_transportation': includeTransportation,
      'status': status,
      'notes': notes,
      'is_active': isActive,
      'is_recurring': isRecurring,
      'end_date': endDate,
      'cancellation_reason': cancellationReason,
      'stop_date': stopDate,
      'stop_reason': stopReason,
    };
  }
}

class Provider {
  final int? id;
  final int? userId;
  final String? name;
  final String? phone;
  final String? image;
  final String? address;
  final String? city;
  final String? district;
  final num? commission;
  final String? idFile;
  final String? idNumber;
  final bool? isSuspended;
  final bool? isApproved;
  final String? skills;
  final String? workZones;
  final String? workingTime;
  final String? holidays;
  final String? tax;
  final String? reviews;
  final num? rating;
  final String? type;
  final String? album;
  final bool? favoriteStatus;
  final List<ProviderServices>? services;

  Provider({
    this.id,
    this.userId,
    this.name,
    this.phone,
    this.image,
    this.address,
    this.city,
    this.district,
    this.commission,
    this.idFile,
    this.idNumber,
    this.isSuspended,
    this.isApproved,
    this.skills,
    this.workZones,
    this.workingTime,
    this.holidays,
    this.tax,
    this.reviews,
    this.rating,
    this.type,
    this.album,
    this.favoriteStatus,
    this.services,
  });

  factory Provider.fromJson(Map<String, dynamic> json) {
    return Provider(
      id: json['id'],
      userId: json['user_id'],
      name: json['name'],
      phone: json['phone'],
      image: json['image'],
      address: json['address'],
      city: json['city'],
      district: json['district'],
      commission: json['commission'],
      idFile: json['id_file'],
      idNumber: json['id_number'],
      isSuspended: json['is_suspended'],
      isApproved: json['is_approved'],
      skills: json['skills'],
      workZones: json['work_zones'],
      workingTime: json['working_time'],
      holidays: json['holidays'],
      tax: json['tax'],
      reviews: json['reviews'],
      rating: json['rating'],
      type: json['type'],
      album: json['album'],
      favoriteStatus: json['favorite_status'],
      services: json['services'] != null
          ? List<ProviderServices>.from(
              json['services'].map((x) => ProviderServices.fromJson(x)))
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'phone': phone,
      'image': image,
      'address': address,
      'city': city,
      'district': district,
      'commission': commission,
      'id_file': idFile,
      'id_number': idNumber,
      'is_suspended': isSuspended,
      'is_approved': isApproved,
      'skills': skills,
      'work_zones': workZones,
      'working_time': workingTime,
      'holidays': holidays,
      'tax': tax,
      'reviews': reviews,
      'rating': rating,
      'type': type,
      'album': album,
      'favorite_status': favoriteStatus,
      'services': services?.map((x) => x.toJson()).toList(),
    };
  }
}
