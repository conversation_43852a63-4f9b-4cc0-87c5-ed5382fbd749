import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/controllers/add_new_service_controller.dart';
import 'package:get_clean/app/modules/pricing/controllers/pricing_controller.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/custom_button.dart';
import '../controllers/edit_service_controller.dart';
import 'widgets/edit_car_widget.dart';
import 'widgets/edit_offer_widget.dart';
import 'widgets/meter_widget.dart';
import 'widgets/sofa_widget.dart';

class EditServiceView extends GetView<EditServiceController> {
  const EditServiceView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditServiceController>(builder: (controller) {
      final withPricingOptions = controller.service.id != null &&
          controller.service.service!.pricingOption!.hasTypes!;

      log('afasfsaf ${controller.service.id}');
      return Scaffold(
        body: Container(
          width: Get.width,
          height: Get.height,
          padding: const EdgeInsets.all(10),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                'assets/images/main_background.png',
              ),
              fit: BoxFit.fill,
            ),
          ),
          child: SafeArea(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      IconButton(
                        onPressed: Get.back,
                        icon: const Icon(
                          CupertinoIcons.back,
                          size: 30,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          Get.find<LanguageController>()
                              .keys
                              .value
                              .editService!,
                          style: bigTextStyle,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text(
                          controller.service.service!.name!,
                          style: big2TextStyle,
                        ),
                      ),
                      const SizedBox(width: 10),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          if (controller.service.id != null)
                            const Icon(
                              FontAwesomeIcons.solidCircle,
                              size: 15,
                            ),
                          const SizedBox(width: 10),
                          Text(
                            controller.service.id == null
                                ? ''
                                : controller
                                    .service.service!.pricingOption!.name!,
                            style: regularTextStyle,
                          ),
                        ],
                      ),
                    ],
                  ).paddingSymmetric(
                    horizontal: 12,
                    vertical: 5,
                  ),
                  if (!isCarService(
                          controller.service.service!.pricingOption?.id) &&
                      !isOffer(controller.service.service!.pricingOption?.id) &&
                      !isClothesService(
                          controller.service.service!.pricingOption?.id))
                    CustomFormField(
                      keyboardType: TextInputType.number,
                      label: Get.find<LanguageController>()
                          .keys
                          .value
                          .materialPrice!,
                      hint: Get.find<LanguageController>()
                          .keys
                          .value
                          .materialPrice!,
                      controller: controller.materialPriceController,
                    ),

                  const SizedBox(
                    height: 10,
                  ),

                  if (isClothesService(
                      controller.service.service!.pricingOption?.id))

                    // deliver switch
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          Get.find<LanguageController>().keys.value.deliver!,
                          style: big2TextStyle,
                        ),
                        CupertinoSwitch(
                          value: controller.isDeliver.value,
                          onChanged: controller.onDeliverChange,
                        ),
                      ],
                    ).paddingSymmetric(
                      horizontal: 12,
                      vertical: 5,
                    ),

                  if (withPricingOptions &&
                      !isCarService(
                          controller.service.service!.pricingOption?.id))
                    const SofaWidget(),

                  if (withPricingOptions &&
                      isCarService(
                          controller.service.service!.pricingOption?.id))
                    const EditCarWidget(),

                  // if (controller.service.id != null &&
                  //     !controller.service.service!.pricingOption!.hasTypes! &&
                  //     !isOffer(controller.service.service!.pricingOption?.id))
                  //   const MetersWidget(),
                  if (isHoursService(
                          controller.service.service!.pricingOption?.id) ||
                      isMeterService(
                          controller.service.service!.pricingOption?.id))
                    const MetersWidget(),

                  if (isOffer(controller.service.service!.pricingOption?.id))
                    const EditOffersWidget(),

                  // SizedBox(height: 20.h),
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.spaceAround,
                  //   children: [
                  //     Row(
                  //       children: [
                  //         Text(
                  //           Get.find<LanguageController>().keys.value.withTax!,
                  //           style: big2TextStyle,
                  //         ),
                  //         Radio(
                  //           activeColor: primaryColor,
                  //           value: true,
                  //           groupValue: controller.withTax.value,
                  //           onChanged: controller.onChangeWithTax,
                  //         ),
                  //       ],
                  //     ),
                  //     Row(
                  //       children: [
                  //         Text(
                  //           Get.find<LanguageController>()
                  //               .keys
                  //               .value
                  //               .withoutTax!,
                  //           style: big2TextStyle,
                  //         ),
                  //         Radio(
                  //           activeColor: primaryColor,
                  //           value: false,
                  //           groupValue: controller.withTax.value,
                  //           onChanged: controller.onChangeWithTax,
                  //         ),
                  //       ],
                  //     ),
                  //   ],
                  // ),
                  SizedBox(height: 30.h),

                  Center(
                    child: CustomButton(
                      label: Get.find<LanguageController>().keys.value.save!,
                      onTap: () async {
                        await controller.editService();

                        Get.back();

                        await Get.find<PricingController>().getMyServices();

                        Get.toNamed(Routes.PRICING);
                      },
                      height: 50.h,
                      width: Get.width * 0.5,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
