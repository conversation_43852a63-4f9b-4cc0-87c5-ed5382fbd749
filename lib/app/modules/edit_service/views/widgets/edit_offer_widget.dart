import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/edit_service/controllers/edit_service_controller.dart';
import 'package:get_clean/global/constants/constants.dart';

class EditOffersWidget extends StatelessWidget {
  const EditOffersWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditServiceController>(
      builder: (controller) {
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: controller.offersList.length,
          itemBuilder: (context, index) {
            final offer = controller.offersList[index];
            final selectedOffers = controller.selectedOffers;

            return CheckboxListTile(
              activeColor: primaryColor,
              fillColor: MaterialStateProperty.all(primaryColor),
              hoverColor: Colors.white,
              checkColor: Colors.white,
              side: MaterialStateBorderSide.resolveWith(
                (states) => const BorderSide(width: 1.0, color: primaryColor),
              ),
              overlayColor: MaterialStateProperty.all(primaryColor),
              title: Text(offer.name ?? ''),
              value: selectedOffers.contains(offer),
              onChanged: (value) {
                if (value!) {
                  controller.selectedOffers.add(offer);
                } else {
                  controller.selectedOffers.remove(offer);
                }
                controller.update();
              },
            );
          },
        );
      },
    );
  }
}
// class OffersWidget extends StatelessWidget {
//   const OffersWidget({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<EditServiceController>(
//       builder: (controller) {
//         return Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             ListView.builder(
//               shrinkWrap: true,
//               physics: const NeverScrollableScrollPhysics(),
//               itemCount: controller.carList.length,
//               itemBuilder: (context, index) {
//                 final car = controller.carList[index];
//                 return Container(
//                   margin: const EdgeInsets.all(10),
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(15),
//                     color: const Color(0xffF3F3F3),
//                   ),
//                   child: ExpansionTile(
//                     initiallyExpanded: true,
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(15),
//                     ),
//                     title: Text(car.name.text),
//                     childrenPadding: const EdgeInsets.all(8),
//                     children: [
//                       Row(
//                         children: [
//                           Expanded(
//                             child: Column(
//                               children: [
//                                 TextWithBackground(
//                                   color: primaryColor,
//                                   text: Get.find<LanguageController>()
//                                       .keys
//                                       .value
//                                       .carType!,
//                                 ),
//                                 for (int i = 0; i < car.carServices.length; i++)
//                                   AddServiceFormField(
//                                     keyboardType: TextInputType.text,
//                                     controller: car.carServices[i].name,
//                                     active: false,
//                                   ),
//                               ],
//                             ),
//                           ),
//                           SizedBox(width: 10.w),
//                           Expanded(
//                             child: Column(
//                               children: [
//                                 Row(
//                                   children: [
//                                     Expanded(
//                                       child: TextWithBackground(
//                                         color: primaryColor,
//                                         text: Get.find<LanguageController>()
//                                             .keys
//                                             .value
//                                             .price!,
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 for (int i = 0; i < car.carServices.length; i++)
//                                   Row(
//                                     children: [
//                                       Expanded(
//                                         child: AddServiceFormField(
//                                           keyboardType: TextInputType.number,
//                                           controller: car.carServices[i].price,
//                                           active: true,
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                               ],
//                             ),
//                           ),
//                         ],
//                       ),
//                     ],
//                   ),
//                 );
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }
// }
