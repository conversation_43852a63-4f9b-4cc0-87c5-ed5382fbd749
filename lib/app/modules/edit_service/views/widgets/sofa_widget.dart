import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/edit_service/controllers/edit_service_controller.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/widget/text_with_background.dart';
import 'edit_service_form_field.dart';

class SofaWidget extends StatelessWidget {
  const SofaWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditServiceController>(
      builder: (controller) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              margin: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: const Color(0xffF3F3F3),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        TextWithBackground(
                          color: primaryColor,
                          text: Get.find<LanguageController>().keys.value.type!,
                          // .sofaType!,//todo-sofa
                        ),
                        for (int i = 0; i < controller.sofaList.length; i++)
                          EditServiceFormField(
                            controller: controller.sofaList[i].name,
                            active: false,
                          ),
                      ],
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: TextWithBackground(
                                color: primaryColor,
                                text: Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .price!,
                              ),
                            ),
                          ],
                        ),
                        for (int i = 0; i < controller.sofaList.length; i++)
                          Row(
                            children: [
                              Expanded(
                                child: EditServiceFormField(
                                  controller: controller.sofaList[i].price,
                                  active: true,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
