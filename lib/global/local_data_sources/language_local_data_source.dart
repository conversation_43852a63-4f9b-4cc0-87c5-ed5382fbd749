import 'package:get_storage/get_storage.dart';
import '../constants/constants.dart';
import '../models/language_model.dart';

class LanguageLocalDataSource {
  // get saved language model from local storage
  Future<LanguageData> getSavedLanguage(LanguagesModel langModel) async {
    final savedLanguage = GetStorage().read(languageKey);

    // return saved language or default language
    if (savedLanguage == null) {
      await GetStorage().write(languageKey, langModel.data!.first.toJson());
      await GetStorage().write(slugKey, langModel.data!.first.slug!);
      return langModel.data!.first;
    } else {
      final savedData = LanguageData.fromJson(savedLanguage);
      await GetStorage()
          .write(slugKey, LanguageData.fromJson(savedLanguage).slug!);

      return langModel.data!
          .firstWhere((element) => element.id == savedData.id);
    }
  }

  // save language model to local storage
  Future<void> saveLanguage(LanguageData languageData) async {
    await GetStorage().write(languageKey, languageData.toJson());
    await GetStorage().write(slugKey, languageData.slug!);
    return;
  }
}
