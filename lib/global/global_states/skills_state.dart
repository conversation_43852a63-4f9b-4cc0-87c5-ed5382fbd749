import 'package:get_clean/global/models/skills_model.dart';

class SkillsState {
  SkillsModel? model;
  String? errorMessage;
}

class SkillsSuccessState extends SkillsState {
  SkillsSuccessState(SkillsModel model) {
    this.model = model;
  }
}

class SkillsErrorState extends SkillsState {
  SkillsErrorState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class SkillsLoadingState extends SkillsState {}
