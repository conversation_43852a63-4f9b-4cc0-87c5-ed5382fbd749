import 'package:get_clean/global/models/offer_appointments_model.dart';

class OfferAppointmentState {
  OfferAppointmentsModel? offerAppointments;
  String? errorMessage;
}

class OfferAppointmentSuccessState extends OfferAppointmentState {
  OfferAppointmentSuccessState(OfferAppointmentsModel offerAppointments) {
    this.offerAppointments = offerAppointments;
  }
}

class OfferAppointmentErrorState extends OfferAppointmentState {
  OfferAppointmentErrorState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}
