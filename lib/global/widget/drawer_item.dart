// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';

import '../constants/theme.dart';

class DrawerItem extends StatelessWidget {
  final onTap, iconData, title;
  const DrawerItem({
    Key? key,
    this.onTap,
    this.iconData,
    this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                iconData,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 10),
              Text(
                title,
                style: regularWhiteTextStyle,
              ),
            ],
          ),
          const Divider(
            color: Colors.white,
          ),
        ],
      ),
    );
  }
}
