// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

class CustomRatingBar extends StatelessWidget {
  final onRatingUpdate;
  final double iconSize;
  final canChangeRate;
  final initialRating;
  final Color starsColor;
  final padding;
  const CustomRatingBar({
    Key? key,
    this.onRatingUpdate,
    required this.iconSize,
    this.canChangeRate = true,
    this.initialRating = 5.0,
    this.starsColor = Colors.amber,
    this.padding = 4.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RatingBar.builder(
      ignoreGestures: canChangeRate,
      initialRating: initialRating.toDouble(),
      minRating: 1,
      direction: Axis.horizontal,
      allowHalfRating: true,
      itemCount: 5,
      itemSize: iconSize,
      itemPadding: EdgeInsets.symmetric(horizontal: padding),
      itemBuilder: (context, _) => Icon(
        Icons.star,
        color: starsColor,
      ),
      onRatingUpdate: onRatingUpdate ?? (value) {},
    );
  }
}
