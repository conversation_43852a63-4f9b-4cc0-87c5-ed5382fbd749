import 'package:flutter/material.dart';
import 'package:get_clean/global/constants/constants.dart';

class TextWithBackground extends StatelessWidget {
  final Color color;
  final String text;
  final Color textColor;
  final double fontSize;
  const TextWithBackground({
    Key? key,
    required this.color,
    required this.text,
    this.textColor = Colors.white,
    this.fontSize = 10.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 7),
      margin: const EdgeInsets.symmetric(vertical: 5),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(9),
        border: Border.all(color: primaryColor),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: fontSize,
        ),
      ),
    );
  }
}
