import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../app/modules/my_profile/controllers/my_profile_controller.dart';
import '../controllers/language_controller.dart';
import 'custom_drop_down_button.dart';

class AreaAndCityBottomSheet extends StatelessWidget {
  const AreaAndCityBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MyProfileController>(builder: (profileController) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          CustomDropDownButton(
            buttonHeight: 50.0.h,
            buttonWidth: Get.width,
            hint: Get.find<LanguageController>().keys.value.chooseArea!,
            value: profileController.choosedAreaForWorkZones.value.name == null
                ? null
                : profileController.choosedAreaForWorkZones.value,
            label: Get.find<LanguageController>().keys.value.area!,
            onChanged: profileController.onChangeChoosedAreaForWorkZones,
            items: profileController.areasForWorkZones.value.data == null
                ? null
                : profileController.areasForWorkZones.value.data!
                    .map(
                      (data) => DropdownMenuItem(
                        value: data,
                        child: Text(
                          data.name!,
                        ),
                      ),
                    )
                    .toList(),
          ),
          CustomDropDownButton(
            buttonHeight: 50.0.h,
            buttonWidth: Get.width,
            hint: Get.find<LanguageController>().keys.value.chooseCity!,
            value: profileController.choosedCityForWorkZones.value.name == null
                ? null
                : profileController.choosedCityForWorkZones.value,
            label: Get.find<LanguageController>().keys.value.city!,
            onChanged: profileController.onChangeChoosedCityForWorkZones,
            items: profileController.citiesForWorkZones.value.data == null
                ? null
                : profileController.citiesForWorkZones.value.data!
                    .map(
                      (data) => DropdownMenuItem(
                        value: data,
                        child: Text(
                          data.name!,
                        ),
                      ),
                    )
                    .toList(),
          ),
        ],
      );
    });
  }
}
