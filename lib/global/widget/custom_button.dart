// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';

import '../constants/constants.dart';

class CustomButton extends StatelessWidget {
  final String label;
  final onTap;
  final double width;
  final double height;
  final color;
  final double fontSize;
  final Widget? icon;
  final double borderRadius;
  final Color? textColor;

  const CustomButton({
    Key? key,
    required this.label,
    required this.onTap,
    required this.height,
    required this.width,
    this.color = primaryColor,
    this.fontSize = 22.0,
    this.icon,
    this.borderRadius = 15.0,
    this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.all(10),
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius),
          color: color,
        ),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Text(
                label,
                style: TextStyle(
                    color: textColor ?? Colors.white, fontSize: fontSize),
                textAlign: TextAlign.center,
              ),
              if (icon != null) icon!,
            ],
          ),
        ),
      ),
    );
  }
}
