import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class MainCachedImage extends StatelessWidget {
  final String? imageUrl;
  final double height;
  final double width;
  final BoxFit? fit;

  const MainCachedImage(
    this.imageUrl, {
    super.key,
    this.height = double.infinity,
    this.width = double.infinity,
    this.fit = BoxFit.fill,
  });

  @override
  Widget build(BuildContext context) {
    final invalidImage =
        imageUrl == null || imageUrl!.isEmpty || imageUrl!.endsWith('/');

    if (invalidImage) {
      return const Icon(Icons.error);
    }

    return CachedNetworkImage(
      imageUrl: imageUrl ?? '',
      height: height,
      width: width,
      fit: fit,
      placeholder: (context, url) =>
          const Center(child: CircularProgressIndicator()),
      errorWidget: (context, url, error) => Padding(
        padding: const EdgeInsets.all(12.0),
        child: const Icon(Icons.error),
      ),
    );
  }
}
