// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/constants.dart';
import '../constants/theme.dart';

class PopularServiceWidget extends StatelessWidget {
  final bool isActive;
  final onTap;
  final title;
  const PopularServiceWidget({
    Key? key,
    required this.isActive,
    this.onTap,
    this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(
          horizontal: 10.w,
        ),
        margin: const EdgeInsets.all(5),
        decoration: BoxDecoration(
          color: isActive ? primaryColor : Colors.white,
          border: Border.all(
            color: primaryColor,
          ),
          borderRadius: BorderRadius.circular(5),
        ),
        child: Text(
          title,
          style: isActive ? smallWhiteTextStyle : smallTextStyle,
        ),
      ),
    );
  }
}
