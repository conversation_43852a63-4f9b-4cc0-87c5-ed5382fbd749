// class LanguagesModel {
//   int? code;
//   bool? success;
//   List<Data>? data;

//   LanguagesModel({this.code, this.success, this.data});

//   LanguagesModel.fromJson(Map<String, dynamic> json) {
//     code = json['code'];
//     success = json['success'];
//     if (json['data'] != null) {
//       data = <Data>[];
//       json['data'].forEach((v) {
//         data!.add(Data.fromJson(v));
//       });
//     }
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['code'] = code;
//     data['success'] = success;
//     if (this.data != null) {
//       data['data'] = this.data!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }

// class Data {
//   int? id;
//   String? name;
//   String? slug;
//   String? keys;

//   Data({this.id, this.name, this.slug, this.keys});

//   Data.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     slug = json['slug'];
//     keys = json['keys'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['slug'] = slug;
//     data['keys'] = keys;
//     return data;
//   }
// }
