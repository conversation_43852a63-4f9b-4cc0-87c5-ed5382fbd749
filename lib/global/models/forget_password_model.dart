class ForgetPaawordModel {
  int? code;
  bool? success;
  Data? data;
  String? message;

  ForgetPaawordModel({this.code, this.success, this.data, this.message});

  ForgetPaawordModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = message;
    return data;
  }
}

class Data {
  int? id;
  String? name;
  String? email;
  String? phone;
  String? address;
  City? city;
  City? district;
  String? gender;
  String? image;
  dynamic birthDate;
  String? type;
  String? accessToken;

  Data(
      {this.id,
      this.name,
      this.email,
      this.phone,
      this.address,
      this.city,
      this.district,
      this.gender,
      this.image,
      this.birthDate,
      this.type,
      this.accessToken});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    address = json['address'];
    city = json['city'] != null ? City.fromJson(json['city']) : null;
    district =
        json['district'] != null ? City.fromJson(json['district']) : null;
    gender = json['gender'];
    image = json['image'];
    birthDate = json['birth_date'];
    type = json['type'];
    accessToken = json['access_token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['phone'] = phone;
    data['address'] = address;
    if (city != null) {
      data['city'] = city!.toJson();
    }
    if (district != null) {
      data['district'] = district!.toJson();
    }
    data['gender'] = gender;
    data['image'] = image;
    data['birth_date'] = birthDate;
    data['type'] = type;
    data['access_token'] = accessToken;
    return data;
  }
}

class City {
  int? id;
  String? name;

  City({this.id, this.name});

  City.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}
