class UserBilling {
  int? code;
  bool? success;
  List<BillingData>? data;

  UserBilling({this.code, this.success, this.data});

  UserBilling.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    if (json['data'] != null) {
      data = <BillingData>[];
      json['data'].forEach((v) {
        data!.add(BillingData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class BillingData {
  int? id;
  String? type;
  num? amount;
  bool? isConfirmed;
  String? date;

  BillingData({this.id, this.type, this.amount, this.isConfirmed, this.date});

  BillingData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    type = json['for'];
    amount = json['amount'];
    isConfirmed = json['is_confirmed'];
    date = json['date'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['for'] = type;
    data['amount'] = amount;
    data['is_confirmed'] = isConfirmed;
    data['date'] = date;
    return data;
  }
}
