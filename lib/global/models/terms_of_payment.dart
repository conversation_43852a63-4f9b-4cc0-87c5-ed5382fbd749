class TermsOfPaymentModel {
  int? code;
  bool? success;
  TermsOfPaymentData? data;

  TermsOfPaymentModel({this.code, this.success, this.data});

  TermsOfPaymentModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data =
        json['data'] != null ? TermsOfPaymentData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class TermsOfPaymentData {
  int? id;
  String? title;
  String? text;

  TermsOfPaymentData({this.id, this.title, this.text});

  TermsOfPaymentData.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? '';
    title = json['title'] ?? 'Terms Of Payment';
    text = json['text'] ?? 'No Data';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['text'] = text;
    return data;
  }
}
