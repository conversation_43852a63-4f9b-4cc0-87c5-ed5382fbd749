// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'city.dart';
import 'district.dart';
import 'provider.dart';

class User {
  int? id;
  String? name;
  String? email;
  String? phone;
  String? address;
  City? district;
  District? city;
  String? gender;
  String? image;
  String? birthDate;
  String? type;
  bool? isVerified;
  String? accessToken;
  Provider? provider;
  String? otp;

  User({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.address,
    this.district,
    this.city,
    this.gender,
    this.image,
    this.birthDate,
    this.type,
    this.isVerified,
    this.accessToken,
    this.provider,
    this.otp,
  });

  User.fromJson(Map<String, dynamic> json) {
    log('asfasafsaf $json');
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    address = json['address'];
    district =
        json['district'] != null ? City.fromJson(json['district']) : null;
    city = json['city'] != null ? District.fromJson(json['city']) : null;
    gender = json['gender'];
    image = json['image'];
    birthDate = json['birth_date'];
    type = json['type'];
    isVerified = json['is_verified'] == 1 ? true : false;
    accessToken = json['access_token'];
    provider = json['provider'] != null
        ? Provider.fromJson(json['provider'])
        : Provider();
    otp = json['otp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['phone'] = phone;
    data['address'] = address;
    if (district != null) {
      data['district'] = district!.toJson();
    }
    if (city != null) {
      data['city'] = city!.toJson();
    }
    data['gender'] = gender;
    data['image'] = image;
    data['birth_date'] = birthDate;
    data['type'] = type;
    data['is_verified'] = isVerified == true ? 1 : 0;
    data['access_token'] = accessToken;
    data['otp'] = otp;
    if (provider != null) {
      data['provider'] = provider!.toJson();
    }
    return data;
  }

  User copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    City? city,
    District? district,
    String? gender,
    String? image,
    String? birthDate,
    String? type,
    bool? isVerified,
    String? accessToken,
    Provider? provider,
    String? otp,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      district: city ?? this.district,
      city: district ?? this.city,
      gender: gender ?? this.gender,
      image: image ?? this.image,
      birthDate: birthDate ?? this.birthDate,
      type: type ?? this.type,
      isVerified: isVerified ?? this.isVerified,
      accessToken: accessToken ?? this.accessToken,
      provider: provider ?? this.provider,
      otp: otp ?? this.otp,
    );
  }
}
