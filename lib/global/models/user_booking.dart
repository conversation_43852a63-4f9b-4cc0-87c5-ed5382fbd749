// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:developer';

import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/models/provider_services.dart';

import 'city.dart';
import 'reviews.dart';
import 'user.dart';

class UserBookings {
  num? code;
  bool? success;
  List<BookingData>? bookingData;

  UserBookings({code, success, data});

  UserBookings.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    if (json['data'] != null) {
      bookingData = <BookingData>[];
      final filteredData = json['data'].toSet().toList();
      log('asfasfasaf ${filteredData}');

      filteredData.forEach((v) {
        bookingData!.add(BookingData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (bookingData != null) {
      data['data'] = bookingData!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class BookingData {
  int? id;
  User? user;
  Provider? provider;
  OrderData? orderData;
  String? orderOptionName;
  String? frequency;
  String? date;
  String? startDate;
  String? day;
  String? startTime;
  num? duration;
  String? status;
  String? group;
  bool? isPending;
  bool? isCanceled;
  bool? isApproved;
  bool? isConfirmed;
  bool? isCompleted;
  bool? canBeCanceled;
  StatusDescrption? statusDescrption;
  String? address;
  City? district;
  City? city;
  num? totalPrice;
  num? providerTotalPrice;
  num? tax;
  num? paidAmount;
  double? deposit;
  num? depositPercentage;
  bool? withDeposit;
  String? userNote;
  String? providerNote;
  Reviews? userReview;
  List<OrderSchedule>? orderSchedule;
  bool? needMaterial;
  num? deliver;
  num? materialPrice;
  num? groupTotal;
  PendingPricingRequest? pendingPricingRequest;
  bool? hasPendingPricingRequest;
  bool? canChat;
  String? bookedAt;
  String? bookedIn;
  bool? canRequestInvoice;
  bool? invoiceRequest;
  String? invoice;
  num? unitPrice;
  num? providerUnitPrice;
  num? userLat;
  num? userLong;

  BookingData({
    id,
    user,
    group,
    provider,
    orderData,
    frequency,
    orderOptionName,
    bookedIn,
    date,
    startDate,
    day,
    groupTotal,
    startTime,
    duration,
    status,
    isPending,
    isCanceled,
    isApproved,
    isConfirmed,
    isCompleted,
    tax,
    canBeCanceled,
    statusDescrption,
    address,
    district,
    city,
    totalPrice,
    totalProviderPrice,
    paidAmount,
    deposit,
    depositPercentage,
    withDeposit,
    userNote,
    providerNote,
    userReview,
    orderSchedule,
    needMaterial,
    deliver,
    materialPrice,
    pendingPricingRequest,
    hadPendingPricingRequest,
    canChat,
    bookedAt,
    canRequestInvoice,
    invoiceRequest,
    invoice,
    bookingAt,
    unitPrice,
    userLat,
    userLong,
    unitProviderPrice,
  });

  BookingData.fromJson(Map<dynamic, dynamic> json) {
    log('DSAasDSS ${json['deliver']}');

    log('PercentageDDD=> ${json['group_total']} Calculated ${json['deposit_precentage']}');

    unitPrice = num.tryParse(json['unit_price'].toString()) ?? 0;
    orderOptionName = json['order_option_name'];

    providerUnitPrice =
        num.tryParse(json['unit_provider_price'].toString()) ?? 0;

    startDate = json['start_date'];
    id = json['id'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    provider =
        json['provider'] != null ? Provider.fromJson(json['provider']) : null;
    orderData = json['order_data'] != null
        ? OrderData.fromJson(json['order_data'])
        : null;
    frequency = json['frequency'];
    if (json['pending_pricing_request'] != null) {
      pendingPricingRequest =
          PendingPricingRequest.fromJson(json['pending_pricing_request']);
    }

    hasPendingPricingRequest = json['has_pending_pricing_requests'] ?? false;
    canChat = json['can_chat'] ?? false;

    bookedAt = json['booked_at'] ?? '';
    groupTotal = num.tryParse(json['group_total']?.toString() ?? '0') ?? 0;
    bookedIn = json['booked_at'] ?? '';
    group = json['group'] ?? '';
    canRequestInvoice = json['can_request_invoice'] ?? false;
    invoiceRequest = json['invoice_request'] ?? false;
    invoice = json['invoice'];

    userLat = json['user_lat'];

    userLong = json['user_long'];
    // num.tryParse(json['user_long'].toString());

    // set date and time
    try {
      date = json['date'].cast<String>()[0];
    } catch (e) {
      date = json['date'];
    }
    try {
      day = json['day'].cast<String>()[0];
    } catch (e) {
      day = json['day'];
    }
    needMaterial = json['need_materials'] == 1;
    deliver = json['deliver'];
    materialPrice = json['material_price'] ?? 0;

    startTime = json['start_time'];
    duration = num.tryParse(json['duration'].toString()) ?? 0;
    status = json['status'];
    // isPending = status == 'pending';
    // isCanceled = status == 'canceled';
    // isApproved = status == 'approved';
    // isConfirmed = status == 'confirmed';
    // isCompleted = status == 'completed';

    isPending = json['is_pending'];
    isCanceled = json['is_canceled'];
    isApproved = json['is_approved'];
    isConfirmed = json['is_confirmed'];
    isCompleted = json['is_completed'];

    canBeCanceled = json['can_be_canceled'];
    statusDescrption = json['status_descrption'] != null
        ? StatusDescrption.fromJson(json['status_descrption'])
        : null;
    address = json['address'];

    district = json['district'] != null
        ? json['district'].runtimeType == String
            ? City(name: json['district'])
            : City.fromJson(json['district'])
        : null;
    city = json['city'] != null
        ? json['city'].runtimeType == String
            ? City(name: json['city'])
            : City.fromJson(json['city'])
        : null;
    totalPrice = json['total_price'] ?? 0;
    providerTotalPrice = json['provider_total_price'] ?? 0;
    tax = json['tax'];
    paidAmount = json['paid_amount'];
    deposit = double.tryParse(json['deposit'].toString()) ?? 0.0;
    depositPercentage = json['deposit_precentage'] ?? 0.0;
    withDeposit = json['with_deposit'] == 1;
    userNote = json['user_note'];
    providerNote = json['provider_note'];
    userReview = json['user_review'] != null
        ? Reviews.fromJson(json['user_review'])
        : null;

    if (json['order_schedule'] != null) {
      orderSchedule = [];
      json['order_schedule'].forEach((e) {
        orderSchedule!.add(OrderSchedule.fromJson(e));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    if (provider != null) {
      data['provider'] = provider!.toJson();
    }
    if (orderData != null) {
      data['order_data'] = orderData!.toJson();
    }

    data['deliver'] = deliver ?? 0;

    data['group_total'] = groupTotal;

    data['order_option_name'] = orderOptionName;

    data['deposit_precentage'] = depositPercentage;

    data['start_date'] = startDate;

    data['booked_at'] = bookedAt;
    data['can_request_invoice'] = canRequestInvoice;
    data['invoice_request'] = invoiceRequest;
    data['invoice'] = invoice;

    data['pending_pricing_request'] = pendingPricingRequest?.toJson();
    data['has_pending_pricing_requests'] = hasPendingPricingRequest;
    data['can_chat'] = canChat;
    data['need_materials'] = needMaterial;
    data['material_price'] = materialPrice;
    data['frequency'] = frequency;
    data['date'] = date;
    data['day'] = day;
    data['group'] = group;
    data['start_time'] = startTime;
    data['duration'] = duration;
    data['status'] = status;
    data['is_pending'] = isPending;
    data['is_canceled'] = isCanceled;
    data['is_approved'] = isApproved;
    data['is_confirmed'] = isConfirmed;
    data['is_completed'] = isCompleted;
    data['booked_at'] = bookedIn;
    data['can_be_canceled'] = canBeCanceled;
    if (statusDescrption != null) {
      data['status_descrption'] = statusDescrption!.toJson();
    }
    data['address'] = address;
    if (district != null) {
      data['district'] = district!.toJson();
    }
    if (city != null) {
      data['city'] = city!.toJson();
    }
    data['total_price'] = totalPrice;
    data['provider_total_price'] = providerTotalPrice;
    data['tax'] = tax;
    data['paid_amount'] = paidAmount;
    data['deposit'] = deposit;
    data['with_deposit'] = withDeposit;
    data['user_note'] = userNote;
    data['provider_note'] = providerNote;
    if (userReview != null) {
      data['user_review'] = userReview!.toJson();
    }
    if (orderSchedule != null) {
      data['order_schedule'] = orderSchedule!.map((e) => e.toJson()).toList();
    }
    data['unit_price'] = unitPrice;
    data['unit_provider_price'] = providerUnitPrice;

    data['user_lat'] = userLat;
    data['user_long'] = userLong;

    return data;
  }
}

// date - note -

class OrderSchedule {
  String? startsAt;
  String? endsAt;

  OrderSchedule({
    this.startsAt,
    this.endsAt,
  });

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'starts_at': startsAt,
      'ends_at': endsAt,
    };
  }

  factory OrderSchedule.fromJson(Map<String, dynamic> map) {
    return OrderSchedule(
      startsAt: map['starts_at'] != null ? map['starts_at'] as String : null,
      endsAt: map['ends_at'] != null ? map['ends_at'] as String : null,
    );
  }
}

// class Services {
//   num? id;
//   String? name;
//   String? image;
//   PricingOption? pricingOption;
//   num? materialPrice;
//   List<PricingList>? pricingList;
//
//   Services({id, name, image, pricingOption, materialPrice, pricingList});
//
//   Services.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     image = json['image'];
//     pricingOption = json['pricing_option'] != null
//         ? PricingOption.fromJson(json['pricing_option'])
//         : null;
//     materialPrice = json['material_price'];
//     if (json['pricing_list'] != null) {
//       pricingList = <PricingList>[];
//       json['pricing_list'].forEach((v) {
//         pricingList!.add(PricingList.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['image'] = image;
//     if (pricingOption != null) {
//       data['pricing_option'] = pricingOption!.toJson();
//     }
//     data['material_price'] = materialPrice;
//     if (pricingList != null) {
//       data['pricing_list'] = pricingList!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }
//
// class PricingOption {
//   num? id;
//   String? name;
//   bool? hasTypes;
//
//   PricingOption({id, name, hasTypes});
//
//   PricingOption.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     hasTypes = json['has_types'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['has_types'] = hasTypes;
//     return data;
//   }
// }
//
// class PricingList {
//   num? from;
//   num? to;
//   double? price;
//   City? type;
//
//   PricingList({from, to, price, type});
//
//   PricingList.fromJson(Map<String, dynamic> json) {
//     from = json['from'];
//     to = json['to'];
//     price = json['price'];
//     type = json['type'] != null ? City.fromJson(json['type']) : null;
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['from'] = from;
//     data['to'] = to;
//     data['price'] = price;
//     if (type != null) {
//       data['type'] = type!.toJson();
//     }
//     return data;
//   }
// }

class WorkAreas {
  num? id;
  String? name;
  City? area;

  WorkAreas({id, name, area});

  WorkAreas.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    area = json['area'] != null ? City.fromJson(json['area']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (area != null) {
      data['area'] = area!.toJson();
    }
    return data;
  }
}

class WorkingTime {
  num? id;
  String? day;
  String? dayName;
  String? startsAt;
  String? endsAt;

  WorkingTime({id, day, dayName, startsAt, endsAt});

  WorkingTime.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    day = json['day'];
    dayName = json['day_name'];
    startsAt = json['starts_at'];
    endsAt = json['ends_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['day'] = day;
    data['day_name'] = dayName;
    data['starts_at'] = startsAt;
    data['ends_at'] = endsAt;
    return data;
  }
}

class OrderData {
  int? id;
  bool isOffer = false;
  String? type;
  List<Types>? types = [];
  Data? bookingData;
  List<TrackingSteps>? trackingSteps = [];

  OrderData({type, data, types});

  OrderData.fromJson(Map<String, dynamic> json) {
    // log('sdfafdsaf $json');
    id = json['data'] != null ? json['data']['id'] : json['id'];
    isOffer = id == 17;
    type = json['type'];
    bookingData = json['data'] != null ? Data.fromJson(json['data']) : null;
    if (json['tracking_steps'] != null) {
      json['tracking_steps'].forEach((item) {
        trackingSteps!.add(TrackingSteps.fromJson(item));
      });
    }

    if (json['types'] != null) {
      json['types'].forEach((item) {
        types!.add(Types.fromJson(item));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    if (bookingData != null) {
      data['data'] = bookingData!.toJson();
    }
    data['id'] = id;
    data['is_offer'] = isOffer;
    data['tracking_steps'] = trackingSteps!.map((e) => e.toJson()).toList();
    data['types'] = types!.map((e) => e.toJson()).toList();
    return data;
  }
}

class TrackingSteps {
  num? id;
  String? name;
  bool? isStepCompleted;

  TrackingSteps({id, name, isStepCompleted});

  TrackingSteps.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    isStepCompleted = json['is_step_completed'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['is_step_completed'] = isStepCompleted;
    return data;
  }
}

//[{id: 3, amount: 2, type: {id: 1, name: Type 01, image: null, carServices: []}}]
class Types {
  num? id;
  num? amount;
  City? type;

  Types({id, amount, type});

  Types.fromJson(Map<String, dynamic> json) {
    log('asfasas $json');
    id = json['id'];
    amount = json['amount'];
    type = json['type'] != null ? City.fromJson(json['type']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['amount'] = amount;
    if (type != null) {
      data['type'] = type!.toJson();
    }
    return data;
  }
}

class Data {
  num? id;
  String? name;
  String? image;
  PricingOption? pricingOption;

  Data({id, name, image, pricingOption});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
    pricingOption = json['pricing_option'] != null
        ? PricingOption.fromJson(json['pricing_option'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['image'] = image;
    if (pricingOption != null) {
      data['pricing_option'] = pricingOption!.toJson();
    }
    return data;
  }
}

class StatusDescrption {
  String? text;
  String? color;

  StatusDescrption({text, color});

  StatusDescrption.fromJson(Map<String, dynamic> json) {
    text = json['text'];
    color = json['color'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['text'] = text;
    data['color'] = color;
    return data;
  }
}

class PendingPricingRequest {
  int? id;
  String? status;
  num? pricingValue;
  PricingOption? pricingOption;

  PendingPricingRequest(
      {this.id, this.status, this.pricingValue, this.pricingOption});

  PendingPricingRequest.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    status = json['status'];
    pricingValue = json['pricing_value'];
    pricingOption = json['pricing_option'] != null
        ? PricingOption.fromJson(json['pricing_option'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['status'] = status;
    data['pricing_value'] = pricingValue;
    if (pricingOption != null) {
      data['pricing_option'] = pricingOption!.toJson();
    }
    return data;
  }
}
