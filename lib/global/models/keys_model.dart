class KeysModel {
  String? name;
  String? login;
  String? yourPhoneNumberOrEmail;
  String? password;
  String? rememberMe;
  String? forgetPassword;
  String? signup;
  String? selectedLanguage;
  String? skip;
  String? aboutUs;
  String? forMoreInfoContactUs;
  String? email;
  String? phone;
  String? popularServices;
  String? services;
  String? congratulation;
  String? welcomeToHomePage;
  String? colsultationRequest;
  String? askForYourAdvice;
  String? financialConsulting;
  String? legalAdvice;
  String? faq;
  String? viewAll;
  String? home;
  String? myOrders;
  String? packages;
  String? languages;
  String? pending;
  String? offerPending;
  String? waitingForVisit;
  String? offerUnPaid;
  String? accepted;
  String? oldOrders;
  String? notifications;
  String? privacyPolicy;
  String? search;
  String? createAnAccount;
  String? fullName;
  String? yourEmail;
  String? yourPhoneNumber;
  String? type;
  String? user;
  String? provider;
  String? company;
  String? termsOfService;
  String? verificationCode;
  String? pleaseEnterVerificationCode;
  String? resendCode;
  String? submit;
  String? companyName;
  String? chooseArea;
  String? area;
  String? chooseCity;
  String? city;
  String? companyAddress;
  String? companyNumber;
  String? companyId;
  String? idFile;
  String? companyPicture;
  String? myProfile;
  String? myBooking;
  String? wallet;
  String? language;
  String? support;
  String? logout;
  String? termsAndConditions;
  String? details;
  String? confirm;
  String? providerName;
  String? providerAddress;
  String? providerNumber;
  String? providerId;
  String? providerPicture;
  String? selectedDay;
  String? done;
  String? address;
  String? requestSentSuccessfully;
  String? agreeWithTermsAndPrivacy;
  String? service;
  String? howManyHours;
  String? hour;
  String? whatTime;
  String? pay;
  String? cancelAllAppointments;
  String? serviceType;
  String? frequency;
  String? duration;
  String? cleaningMaterial;
  String? paymentDetails;
  String? finalPrice;
  String? deposit20;
  String? status;
  String? payment;
  String? payTheRestOfTheAmount;
  String? next;
  String? cancel;
  String? addCard;
  String? weAccept;
  String? rememberThisCard;
  String? rememberThisCardText;
  String? paymentCompeletedSuccessfully;
  String? rateYourProvider;
  String? writeComment;
  String? rate;
  String? yes;
  String? no;
  String? cancelRequestMessage;
  String? ifYouCancelYouLoseMoney;
  String? orderCanceledSuccessfully;
  String? cancelThisAppointment;
  String? orderDetails;
  String? paymentMethod;
  String? cash;
  String? deposit;
  String? total;
  String? pleaseEnterPhoneNumber;
  String? phoneNumber;
  String? didntreceiverotp;
  String? newPassword;
  String? passwordChangedSuccessfully;
  String? workingTimes;
  String? holidays;
  String? makeRequest;
  String? skills;
  String? workAreas;
  String? salary;
  String? days;
  String? open;
  String? close;
  String? from;

  String? to;
  String? reviews;
  String? changePassword;
  String? gender;
  String? age;
  String? save;
  String? providerPackages;
  String? urgentRequest;
  String? today;
  String? iNeedMaterial;
  String? howManyTimes;
  String? onece;
  String? weekly;
  String? monthly;
  String? note;
  String? selectDays;
  String? mon;
  String? tue;
  String? wed;
  String? thu;
  String? fri;
  String? sat;
  String? sun;
  String? providerWillContactYou;
  String? gotoOrderDetialsPage;
  String? pleaseEnterFullName;
  String? pleaseEnterEmail;
  String? pleaseEnterPhone;
  String? pleaseEnterPassword;
  String? pleaseEnterYourAddress;
  String? pleaseChooseCity;
  String? pleaseChooseArea;
  String? pleaseEnterProviderName;
  String? pleaseEnterProviderAddress;
  String? pleaseEnterProviderPhoneNumber;
  String? pleaseEnterProviderIdNumber;
  String? beAsUser;
  String? minHours;
  String? pleaseEnterChooseProviderImage;
  String? pleaseEnterCompanyName;
  String? pleaseEnterCompanyAddress;
  String? pleaseEnterCompanyPhoneNumber;
  String? pleaseEnterCompanyIdNumber;
  String? pleaseEnterChooseCompanyImage;
  String? pleaseEnterEmailOrPassword;
  String? pleaseInsertCode;
  String? pleaseInsertCorrectCode;
  String? newPasswordConfirmation;
  String? pleaseInsertPassword;
  String? pleaseInsertPasswordConfirmation;
  String? yourBirthDate;
  String? pleaseInsertYourBirthDate;
  String? ok;
  String? add;
  String? editSchduel;
  String? schduelYourOrders;
  String? failedToLoad;
  String? locationSavedSuccessfully;
  String? deleteWorkingTime;
  String? deleteWorkingTimeMessage;
  String? fromDateHoliday;
  String? toDateHoliday;
  String? deleteHoliday;
  String? deleteHolidayMessage;
  String? pleaseChooseDaysFirst;
  String? pricing;
  String? addYourPriceForEachCategory;
  String? news;
  String? addNewService;
  String? materialPrice;
  String? price;
  String? sofaType;
  String? carType;
  String? withTax;
  String? withoutTax;
  String? deleteService;
  String? deleteServiceMessage;
  String? offers;
  String? myOffers;
  String? bookNow;
  String? addOffer;
  String? offerDetails;
  String? averageTime;
  String? description;
  String? deleteOffer;
  String? deleteOfferMessage;
  String? edit;
  String? delete;
  String? isActive;
  String? male;
  String? female;
  String? editService;
  String? newOffers;
  String? onlyFor;
  String? ils;
  String? groupTotal;
  String? providerAvilableTimes;
  String? howManyMeters;
  String? pleaseWait;
  String? calculatePrice;
  String? approveThisOrder;
  String? orderApprovedSuccessfully;
  String? compeleted;
  String? finishClean;
  String? orderCompeletedSuccessfully;
  String? meter;
  String? sendToAll;
  String? message;
  String? sofas;
  String? everyOne;
  String? noAvilableTimes;
  String? submitNewOffer;
  String? trackYourOrder;
  String? uploadInvoice;
  String? orderSchedule;
  String? startsAt;
  String? endsAt;
  String? addTip;
  String? payDeposit;
  String? requestInvoice;
  String? amount;
  String? needAction;
  String? todo;
  String? requestExtraTime;
  String? theRequired;
  String? requestedExtraTime;
  String? areYouAccept;
  String? accept;
  String? reject;
  String? showInvoice;
  String? date;
  String? count;
  String? bookingTime;
  String? noAvilableProviders;
  String? tips;
  String? restartApp;
  String? restartAppContent;
  String? showTimes;
  String? unPaidOrders;
  String? more;
  String? hourPrice;
  String? meterPrice;
  String? hoursNumber;
  String? metersNumber;
  String? filterOrders;
  String? deleteProfile;
  String? deleteProfileMessage;
  String? pleaseChooseAtLeaseOneSkill;
  String? setLocationInMap;
  String? discountAmount;
  String? shouldLoginFirst;

  String? albums;
  String? addAlbum;
  String? editAlbum;
  String? addImages;
  String? images;
  String? favorites;
  String? myAlbums;
  String? calculateOffer;
  String? appCommission;
  String? tax;
  String? startDate;
  String? endDate;
  String? myProviderOfferServices;
  String? myOfferServices;
  String? addMore;
  String? shouldHaveAtLeastOne;
  String? multi;
  String? deleteAlbum;
  String? deleteImageMessage;
  String? deleteAlbumMessage;
  String? myFavorites;
  String? waiting;
  String? received;
  String? waitingForApproval;
  String? deliver;
  String? orderEditedSuccessfully;
  String? myCart;

  //? Not Added
  String? pleaseEnterLocationName;
  String? pleaseSelectLocation;
  String? pleaseEnter;
  String? deleteLocation;
  String? deleteLocationConfirmation;
  String? jobs;
  String? myJobs;
  String? addJob;
  String? editJob;
  String? deleteJob;
  String? deleteJobConfirmation;
  String? jobDetails;
  String? noJobsFound;
  String? selectService;
  String? selectLocation;
  String? notes;
  String? schedule;
  String? monday;
  String? tuesday;
  String? wednesday;
  String? thursday;
  String? friday;
  String? saturday;
  String? sunday;
  String? startTime;
  String? hours;
  String? pleaseSetSchedule;
  String? jobApplications;
  String? myJobApplications;
  String? noJobApplicationsFound;
  String? jobApplicationDetails;
  String? workDays;
  String? scheduleHours;
  String? cost;
  String? calendarJobs;
  String? jobsFor;
  String? noJobsForThisDay;
  String? order;
  String? jobApplication;
  String? orderNumber;
  String? start;
  String? end;
  String? approved;
  String? absent;
  String? notStarted;
  String? ended;
  String? confirmed;
  String? notWithinWorkLocation;
  String? taskStartedSuccessfully;
  String? taskEndedSuccessfully;
  String? success;
  String? workLocations;
  String? noWorkLocationsFound;
  String? enterLocationName;
  String? locationName;
  String? longitude;
  String? latitude;
  String? viewOnMap;
  String? warning;
  String? areYouSureYouWantToMakeThisAction;
  String? financialReport;
  String? filterByPeriod;
  String? month;
  String? year;
  String? totalTransportation;
  String? totalAmount;
  String? grandTotal;
  String? totalTasks;
  String? totalHours;
  String? noTasksFound;
  String? showAllTasks;
  String? hideAllTasks;
  String? executionDate;
  String? time;
  String? location;
  String? minutes;
  String? unpaidAmount;
  String? payNow;
  String? closedFinancially;
  String? unClosedFinancially;

  KeysModel({
    this.name,
    this.login,
    this.yourPhoneNumberOrEmail,
    this.password,
    this.rememberMe,
    this.forgetPassword,
    this.signup,
    this.selectedLanguage,
    this.skip,
    this.aboutUs,
    this.forMoreInfoContactUs,
    this.email,
    this.phone,
    this.popularServices,
    this.services,
    this.congratulation,
    this.welcomeToHomePage,
    this.colsultationRequest,
    this.askForYourAdvice,
    this.financialConsulting,
    this.legalAdvice,
    this.faq,
    this.viewAll,
    this.home,
    this.myOrders,
    this.packages,
    this.multi,
    this.languages,
    this.pending,
    this.offerPending,
    this.waitingForVisit,
    this.offerUnPaid,
    this.accepted,
    this.oldOrders,
    this.notifications,
    this.privacyPolicy,
    this.search,
    this.createAnAccount,
    this.fullName,
    this.yourEmail,
    this.yourPhoneNumber,
    this.type,
    this.user,
    this.provider,
    this.company,
    this.termsOfService,
    this.verificationCode,
    this.locationSavedSuccessfully,
    this.pleaseEnterVerificationCode,
    this.resendCode,
    this.submit,
    this.companyName,
    this.chooseArea,
    this.area,
    this.chooseCity,
    this.city,
    this.companyAddress,
    this.companyNumber,
    this.companyId,
    this.idFile,
    this.companyPicture,
    this.myProfile,
    this.myBooking,
    this.wallet,
    this.language,
    this.support,
    this.logout,
    this.termsAndConditions,
    this.details,
    this.confirm,
    this.providerName,
    this.providerAddress,
    this.providerNumber,
    this.providerId,
    this.providerPicture,
    this.selectedDay,
    this.done,
    this.address,
    this.requestSentSuccessfully,
    this.agreeWithTermsAndPrivacy,
    this.service,
    this.howManyHours,
    this.hour,
    this.whatTime,
    this.pay,
    this.cancelAllAppointments,
    this.serviceType,
    this.frequency,
    this.duration,
    this.cleaningMaterial,
    this.paymentDetails,
    this.finalPrice,
    this.deposit20,
    this.status,
    this.payment,
    this.payTheRestOfTheAmount,
    this.next,
    this.cancel,
    this.addCard,
    this.weAccept,
    this.rememberThisCard,
    this.rememberThisCardText,
    this.paymentCompeletedSuccessfully,
    this.finishClean,
    this.rateYourProvider,
    this.writeComment,
    this.rate,
    this.yes,
    this.no,
    this.cancelRequestMessage,
    this.ifYouCancelYouLoseMoney,
    this.orderCanceledSuccessfully,
    this.cancelThisAppointment,
    this.orderDetails,
    this.paymentMethod,
    this.cash,
    this.deposit,
    this.total,
    this.pleaseEnterPhoneNumber,
    this.phoneNumber,
    this.didntreceiverotp,
    this.newPassword,
    this.passwordChangedSuccessfully,
    this.workingTimes,
    this.holidays,
    this.makeRequest,
    this.skills,
    this.workAreas,
    this.salary,
    this.days,
    this.open,
    this.close,
    this.from,
    this.to,
    this.reviews,
    this.changePassword,
    this.gender,
    this.age,
    this.save,
    this.providerPackages,
    this.urgentRequest,
    this.today,
    this.iNeedMaterial,
    this.howManyTimes,
    this.onece,
    this.weekly,
    this.monthly,
    this.note,
    this.selectDays,
    this.mon,
    this.tue,
    this.wed,
    this.thu,
    this.fri,
    this.sat,
    this.sun,
    this.providerWillContactYou,
    this.gotoOrderDetialsPage,
    this.pleaseEnterFullName,
    this.pleaseEnterEmail,
    this.pleaseEnterPhone,
    this.pleaseEnterPassword,
    this.pleaseEnterYourAddress,
    this.pleaseChooseCity,
    this.pleaseChooseArea,
    this.pleaseEnterProviderName,
    this.pleaseEnterProviderAddress,
    this.pleaseEnterProviderPhoneNumber,
    this.pleaseEnterProviderIdNumber,
    this.beAsUser,
    this.minHours,
    this.pleaseEnterChooseProviderImage,
    this.pleaseEnterCompanyName,
    this.pleaseEnterCompanyAddress,
    this.pleaseEnterCompanyPhoneNumber,
    this.pleaseEnterCompanyIdNumber,
    this.pleaseEnterChooseCompanyImage,
    this.pleaseEnterEmailOrPassword,
    this.pleaseInsertCode,
    this.pleaseInsertCorrectCode,
    this.newPasswordConfirmation,
    this.pleaseInsertPassword,
    this.pleaseInsertPasswordConfirmation,
    this.yourBirthDate,
    this.pleaseInsertYourBirthDate,
    this.ok,
    this.add,
    this.editSchduel,
    this.schduelYourOrders,
    this.failedToLoad,
    this.deleteWorkingTime,
    this.deleteWorkingTimeMessage,
    this.fromDateHoliday,
    this.toDateHoliday,
    this.deleteHoliday,
    this.deleteHolidayMessage,
    this.pleaseChooseDaysFirst,
    this.pricing,
    this.addYourPriceForEachCategory,
    this.news,
    this.addNewService,
    this.materialPrice,
    this.price,
    this.sofaType,
    this.carType,
    this.withTax,
    this.withoutTax,
    this.deleteService,
    this.deleteServiceMessage,
    this.offers,
    this.myOffers,
    this.bookNow,
    this.addOffer,
    this.offerDetails,
    this.averageTime,
    this.description,
    this.deleteOffer,
    this.deleteOfferMessage,
    this.edit,
    this.delete,
    this.isActive,
    this.male,
    this.female,
    this.editService,
    this.newOffers,
    this.onlyFor,
    this.ils,
    this.providerAvilableTimes,
    this.howManyMeters,
    this.pleaseWait,
    this.calculatePrice,
    this.approveThisOrder,
    this.orderApprovedSuccessfully,
    this.compeleted,
    this.orderCompeletedSuccessfully,
    this.meter,
    this.sendToAll,
    this.message,
    this.sofas,
    this.everyOne,
    this.noAvilableTimes,
    this.submitNewOffer,
    this.trackYourOrder,
    this.uploadInvoice,
    this.orderSchedule,
    this.startsAt,
    this.endsAt,
    this.addTip,
    this.payDeposit,
    this.requestInvoice,
    this.amount,
    this.requestExtraTime,
    this.theRequired,
    this.requestedExtraTime,
    this.areYouAccept,
    this.accept,
    this.reject,
    this.showInvoice,
    this.date,
    this.count,
    this.bookingTime,
    this.noAvilableProviders,
    this.tips,
    this.restartApp,
    this.restartAppContent,
    this.showTimes,
    this.unPaidOrders,
    this.more,
    this.hourPrice,
    this.meterPrice,
    this.hoursNumber,
    this.metersNumber,
    this.filterOrders,
    this.deleteProfile,
    this.deleteProfileMessage,
    this.pleaseChooseAtLeaseOneSkill,
    this.setLocationInMap,
    this.discountAmount,
    this.shouldLoginFirst,
    this.albums,
    this.addAlbum,
    this.editAlbum,
    this.addImages,
    this.images,
    this.favorites,
    this.myAlbums,
    this.calculateOffer,
    this.appCommission,
    this.tax,
    this.startDate,
    this.endDate,
    this.myProviderOfferServices,
    this.myOfferServices,
    this.addMore,
    this.shouldHaveAtLeastOne,
    this.deleteAlbum,
    this.deleteImageMessage,
    this.deleteAlbumMessage,
    this.myFavorites,
    this.waiting,
    this.received,
    this.waitingForApproval,
    this.deliver,
    this.orderEditedSuccessfully,
    this.myCart,
    this.needAction,
    this.todo,
    this.pleaseEnterLocationName,
    this.pleaseSelectLocation,
    this.pleaseEnter,
    this.deleteLocation,
    this.deleteLocationConfirmation,
    this.workLocations,
    this.noWorkLocationsFound,
    this.enterLocationName,
    this.locationName,
    this.longitude,
    this.latitude,
    this.viewOnMap,

    // Jobs
    this.jobs,
    this.myJobs,
    this.addJob,
    this.editJob,
    this.deleteJob,
    this.deleteJobConfirmation,
    this.jobDetails,
    this.noJobsFound,
    this.selectService,
    this.selectLocation,
    this.notes,
    this.schedule,
    this.monday,
    this.tuesday,
    this.wednesday,
    this.thursday,
    this.friday,
    this.saturday,
    this.sunday,
    this.startTime,
    this.hours,
    this.pleaseSetSchedule,

    // Job Applications
    this.jobApplications,
    this.myJobApplications,
    this.noJobApplicationsFound,
    this.jobApplicationDetails,
    this.workDays,
    this.scheduleHours,
    this.cost,

    // Calendar Jobs
    this.calendarJobs,
    this.jobsFor,
    this.noJobsForThisDay,
    this.order,
    this.jobApplication,
    this.orderNumber,
    this.start,
    this.end,
    this.approved,
    this.absent,
    this.notStarted,
    this.ended,
    this.confirmed,
    this.notWithinWorkLocation,
    this.taskStartedSuccessfully,
    this.taskEndedSuccessfully,
    this.success,
    // Calendar Reports
    this.financialReport,
    this.filterByPeriod,
    this.month,
    this.year,
    this.totalTransportation,
    this.totalAmount,
    this.grandTotal,
    this.totalTasks,
    this.totalHours,
    this.noTasksFound,
    this.showAllTasks,
    this.hideAllTasks,
    this.executionDate,
    this.time,
    this.minutes,
    this.unpaidAmount,
    this.payNow,
    this.unClosedFinancially,
    this.closedFinancially,
  });

  KeysModel.fromJson(Map<String, dynamic> json) {
    name = json['name'] ?? '';
    discountAmount = json['discount_amount'] ?? 'Discount Amount';
    shouldLoginFirst = json['should_login_first'] ?? 'You Should Login First';
    login = json['login'] ?? '';
    yourPhoneNumberOrEmail = json['yourPhoneNumberOrEmail'] ?? '';
    pleaseChooseAtLeaseOneSkill = json['pleaseChooseAtLeaseOneSkill'] ?? '';
    password = json['password'] ?? '';
    rememberMe = json['rememberMe'] ?? '';
    forgetPassword = json['forgetPassword'] ?? '';
    signup = json['signup'] ?? '';
    selectedLanguage = json['selectedLanguage'] ?? '';
    skip = json['skip'] ?? '';
    aboutUs = json['aboutUs'] ?? '';
    forMoreInfoContactUs = json['forMoreInfoContactUs'] ?? '';
    email = json['email'] ?? '';
    phone = json['phone'] ?? '';
    popularServices = json['popularServices'] ?? '';
    services = json['services'] ?? '';
    congratulation = json['congratulation'] ?? '';
    welcomeToHomePage = json['welcomeToHomePage'] ?? '';
    colsultationRequest = json['colsultationRequest'] ?? '';
    askForYourAdvice = json['askForYourAdvice'] ?? '';
    financialConsulting = json['financialConsulting'] ?? '';
    legalAdvice = json['legalAdvice'] ?? '';
    faq = json['faq'] ?? '';
    viewAll = json['viewAll'] ?? '';
    home = json['home'] ?? '';
    myOrders = json['myOrders'] ?? '';
    packages = json['packages'] ?? '';
    languages = json['languages'] ?? '';
    pending = json['pending'] ?? '';
    offerPending = json['offerPending'] ?? "Pending";
    locationSavedSuccessfully =
        json['offerPending'] ?? "Location Saved Successfully";
    waitingForVisit = json['waitingForVisit'] ?? "Waiting For Visit";
    offerUnPaid = json['offerUnPaid'] ?? "UnPaid";
    accepted = json['accepted'] ?? '';
    oldOrders = json['oldOrders'] ?? '';
    notifications = json['notifications'] ?? '';
    privacyPolicy = json['privacyPolicy'] ?? '';
    search = json['search'] ?? '';
    createAnAccount = json['createAnAccount'] ?? '';
    fullName = json['fullName'] ?? '';
    yourEmail = json['yourEmail'] ?? '';
    yourPhoneNumber = json['yourPhoneNumber'] ?? '';
    type = json['type'] ?? '';
    user = json['user'] ?? '';
    provider = json['provider'] ?? '';
    company = json['company'] ?? '';
    termsOfService = json['termsOfService'] ?? '';
    verificationCode = json['verificationCode'] ?? '';
    pleaseEnterVerificationCode = json['pleaseEnterVerificationCode'] ?? '';
    resendCode = json['resendCode'] ?? '';
    submit = json['submit'] ?? '';
    companyName = json['companyName'] ?? '';
    chooseArea = json['chooseArea'] ?? '';
    area = json['area'] ?? '';
    chooseCity = json['chooseCity'] ?? '';
    city = json['city'] ?? '';
    companyAddress = json['companyAddress'] ?? '';
    companyNumber = json['companyNumber'] ?? '';
    companyId = json['companyId'] ?? '';
    idFile = json['idFile'] ?? '';
    companyPicture = json['companyPicture'] ?? '';
    myProfile = json['myProfile'] ?? '';
    myBooking = json['myBooking'] ?? '';
    wallet = json['wallet'] ?? '';
    language = json['language'] ?? '';
    support = json['support'] ?? '';
    logout = json['logout'] ?? '';
    termsAndConditions = json['termsAndConditions'] ?? '';
    details = json['details'] ?? '';
    confirm = json['confirm'] ?? '';
    providerName = json['providerName'] ?? '';
    providerAddress = json['providerAddress'] ?? '';
    providerNumber = json['providerNumber'] ?? '';
    providerId = json['providerId'] ?? '';
    providerPicture = json['providerPicture'] ?? '';
    selectedDay = json['selectedDay'] ?? '';
    done = json['done'] ?? '';
    address = json['address'] ?? '';
    requestSentSuccessfully = json['requestSentSuccessfully'] ?? '';
    agreeWithTermsAndPrivacy = json['agreeWithTermsAndPrivacy'] ?? '';
    service = json['service'] ?? '';
    howManyHours = json['howManyHours'] ?? '';
    hour = json['hour'] ?? '';
    whatTime = json['whatTime'] ?? '';
    pay = json['pay'] ?? '';
    cancelAllAppointments = json['cancelAllAppointments'] ?? '';
    serviceType = json['serviceType'] ?? '';
    frequency = json['frequency'] ?? '';
    duration = json['duration'] ?? '';
    cleaningMaterial = json['cleaningMaterial'] ?? '';
    paymentDetails = json['paymentDetails'] ?? '';
    finalPrice = json['finalPrice'] ?? '';
    deposit20 = json['deposit20'] ?? '';
    status = json['status'] ?? '';
    payment = json['payment'] ?? '';
    payTheRestOfTheAmount = json['payTheRestOfTheAmount'] ?? '';
    next = json['next'] ?? '';
    cancel = json['cancel'] ?? '';
    addCard = json['addCard'] ?? '';
    weAccept = json['weAccept'] ?? '';
    rememberThisCard = json['rememberThisCard'] ?? '';
    rememberThisCardText = json['rememberThisCardText'] ?? '';
    paymentCompeletedSuccessfully = json['paymentCompeletedSuccessfully'] ?? '';
    rateYourProvider = json['rateYourProvider'] ?? '';
    writeComment = json['writeComment'] ?? '';
    rate = json['rate'] ?? '';
    yes = json['yes'] ?? '';
    no = json['no'] ?? '';
    cancelRequestMessage = json['cancelRequestMessage'] ?? '';
    ifYouCancelYouLoseMoney = json['ifYouCancelYouLoseMoney'] ?? '';
    orderCanceledSuccessfully = json['orderCanceledSuccessfully'] ?? '';
    cancelThisAppointment = json['cancelThisAppointment'] ?? '';
    orderDetails = json['orderDetails'] ?? '';
    paymentMethod = json['paymentMethod'] ?? '';
    cash = json['cash'] ?? '';
    deposit = json['deposit'] ?? '';
    total = json['total'] ?? '';
    pleaseEnterPhoneNumber = json['pleaseEnterPhoneNumber'] ?? '';
    phoneNumber = json['phoneNumber'] ?? '';
    didntreceiverotp = json['didntreceiverotp'] ?? '';
    newPassword = json['newPassword'] ?? '';
    passwordChangedSuccessfully = json['passwordChangedSuccessfully'] ?? '';
    workingTimes = json['workingTimes'] ?? '';
    holidays = json['holidays'] ?? '';
    makeRequest = json['makeRequest'] ?? '';
    skills = json['skills'] ?? '';
    workAreas = json['workAreas'] ?? '';
    salary = json['salary'] ?? '';
    days = json['days'] ?? '';
    open = json['open'] ?? '';
    close = json['close'] ?? '';
    from = json['from'] ?? '';
    to = json['to'] ?? '';
    reviews = json['reviews'] ?? '';
    changePassword = json['changePassword'] ?? '';
    gender = json['gender'] ?? '';
    age = json['age'] ?? '';
    save = json['save'] ?? '';
    providerPackages = json['providerPackages'] ?? '';
    urgentRequest = json['urgentRequest'] ?? '';
    today = json['today'] ?? '';
    iNeedMaterial = json['iNeedMaterial'] ?? '';
    howManyTimes = json['howManyTimes'] ?? '';
    onece = json['onece'] ?? '';
    weekly = json['weekly'] ?? '';
    monthly = json['monthly'] ?? '';
    note = json['note'] ?? '';
    selectDays = json['selectDays'] ?? '';
    mon = json['mon'] ?? '';
    tue = json['tue'] ?? '';
    wed = json['wed'] ?? '';
    thu = json['thu'] ?? '';
    fri = json['fri'] ?? '';
    sat = json['sat'] ?? '';
    sun = json['sun'] ?? '';
    providerWillContactYou = json['providerWillContactYou'] ?? '';
    gotoOrderDetialsPage = json['gotoOrderDetialsPage'] ?? '';
    pleaseEnterFullName = json['pleaseEnterFullName'] ?? '';
    pleaseEnterEmail = json['pleaseEnterEmail'] ?? '';
    pleaseEnterPhone = json['pleaseEnterPhone'] ?? '';
    pleaseEnterPassword = json['pleaseEnterPassword'] ?? '';
    pleaseEnterYourAddress = json['pleaseEnterYourAddress'] ?? '';
    pleaseChooseCity = json['pleaseChooseCity'] ?? '';
    pleaseChooseArea = json['pleaseChooseArea'] ?? '';
    pleaseEnterProviderName = json['pleaseEnterProviderName'] ?? '';
    pleaseEnterProviderAddress = json['pleaseEnterProviderAddress'] ?? '';
    pleaseEnterProviderPhoneNumber =
        json['pleaseEnterProviderPhoneNumber'] ?? '';
    pleaseEnterProviderIdNumber = json['pleaseEnterProviderIdNumber'] ?? '';
    pleaseEnterChooseProviderImage =
        json['pleaseEnterChooseProviderImage'] ?? '';
    pleaseEnterCompanyName = json['pleaseEnterCompanyName'] ?? '';
    pleaseEnterCompanyAddress = json['pleaseEnterCompanyAddress'] ?? '';
    pleaseEnterCompanyPhoneNumber = json['pleaseEnterCompanyPhoneNumber'] ?? '';
    pleaseEnterCompanyIdNumber = json['pleaseEnterCompanyIdNumber'] ?? '';
    pleaseEnterChooseCompanyImage = json['pleaseEnterChooseCompanyImage'] ?? '';
    pleaseEnterEmailOrPassword = json['pleaseEnterEmailOrPassword'] ?? '';
    pleaseInsertCode = json['pleaseInsertCode'] ?? '';
    pleaseInsertCorrectCode = json['pleaseInsertCorrectCode'] ?? '';
    newPasswordConfirmation = json['newPasswordConfirmation'] ?? '';
    pleaseInsertPassword = json['pleaseInsertPassword'] ?? '';
    pleaseInsertPasswordConfirmation =
        json['pleaseInsertPasswordConfirmation'] ?? '';
    yourBirthDate = json['yourBirthDate'] ?? '';
    pleaseInsertYourBirthDate = json['pleaseInsertYourBirthDate'] ?? '';
    ok = json['ok'] ?? '';
    add = json['add'] ?? '';
    editSchduel = json['editSchduel'] ?? '';
    schduelYourOrders = json['schduelYourOrders'] ?? '';
    failedToLoad = json['failedToLoad'] ?? '';
    deleteWorkingTime = json['deleteWorkingTime'] ?? '';
    deleteWorkingTimeMessage = json['deleteWorkingTimeMessage'] ?? '';
    fromDateHoliday = json['fromDateHoliday'] ?? '';
    toDateHoliday = json['toDateHoliday'] ?? '';
    deleteHoliday = json['deleteHoliday'] ?? '';
    deleteHolidayMessage = json['deleteHolidayMessage'] ?? '';
    pleaseChooseDaysFirst = json['pleaseChooseDaysFirst'] ?? '';
    pricing = json['pricing'] ?? '';
    addYourPriceForEachCategory = json['addYourPriceForEachCategory'] ?? '';
    news = json['news'] ?? '';
    addNewService = json['addNewService'] ?? '';
    materialPrice = json['materialPrice'] ?? '';
    price = json['price'] ?? '';
    sofaType = json['sofaType'] ?? '';
    carType = json['car_type'] ?? 'Car Type';
    withTax = json['withTax'] ?? '';
    withoutTax = json['withoutTax'] ?? '';
    deleteService = json['deleteService'] ?? '';
    deleteServiceMessage = json['deleteServiceMessage'] ?? '';
    offers = json['offers'] ?? '';
    myOffers = json['myOffers'] ?? '';
    bookNow = json['bookNow'] ?? '';
    addOffer = json['addOffer'] ?? '';
    offerDetails = json['offerDetails'] ?? '';
    averageTime = json['averageTime'] ?? '';
    description = json['description'] ?? '';
    deleteOffer = json['deleteOffer'] ?? '';
    deleteOfferMessage = json['deleteOfferMessage'] ?? '';
    edit = json['edit'] ?? '';
    delete = json['delete'] ?? '';
    isActive = json['isActive'] ?? '';
    male = json['male'] ?? '';
    female = json['female'] ?? '';
    editService = json['editService'] ?? '';
    newOffers = json['newOffers'] ?? '';
    onlyFor = json['onlyFor'] ?? '';
    ils = json['ils'] ?? '';
    providerAvilableTimes = json['providerAvilableTimes'] ?? '';
    howManyMeters = json['howManyMeters'] ?? '';
    pleaseWait = json['pleaseWait'] ?? '';
    calculatePrice = json['calculatePrice'] ?? '';
    approveThisOrder = json['approveThisOrder'] ?? '';
    orderApprovedSuccessfully = json['orderApprovedSuccessfully'] ?? '';
    compeleted = json['compeleted'] ?? '';
    orderCompeletedSuccessfully = json['orderCompeletedSuccessfully'] ?? '';
    meter = json['meter'] ?? '';
    sendToAll = json['sendToAll'] ?? '';
    message = json['message'] ?? '';
    sofas = json['sofas'] ?? '';
    everyOne = json['everyOne'] ?? '';
    noAvilableTimes = json['noAvilableTimes'] ?? '';
    submitNewOffer = json['submitNewOffer'] ?? '';
    trackYourOrder = json['trackYourOrder'] ?? '';
    uploadInvoice = json['uploadInvoice'] ?? '';
    orderSchedule = json['orderSchedule'] ?? '';
    startsAt = json['startsAt'] ?? '';
    endsAt = json['endsAt'] ?? '';
    addTip = json['addTip'] ?? '';
    payDeposit = json['payDeposit'] ?? '';
    requestInvoice = json['requestInvoice'] ?? '';
    amount = json['amount'] ?? '';
    requestExtraTime = json['requestExtraTime'] ?? '';
    theRequired = json['theRequired'] ?? '';
    requestedExtraTime = json['requestedExtraTime'] ?? '';
    areYouAccept = json['areYouAccept'] ?? '';
    accept = json['accept'] ?? '';
    reject = json['reject'] ?? '';
    showInvoice = json['showInvoice'] ?? '';
    date = json['date'] ?? '';
    count = json['count'] ?? '';
    bookingTime = json['bookingTime'] ?? '';
    noAvilableProviders = json['noAvilableProviders'] ?? '';
    tips = json['tips'] ?? '';
    restartApp = json['restartApp'] ?? "Restart App";
    restartAppContent = json['restartAppContent'] ??
        "We Will Restart The App To Change Language";
    showTimes = json['showTimes'] ?? "Show Times";
    unPaidOrders = json['unPaidOrders'] ?? "Unpaid Orders";
    more = json['more'] ?? "More";
    meterPrice = json['meterPrice'] ?? "Meter Price";
    hourPrice = json['hourPrice'] ?? "Hour Price";
    hoursNumber = json['hoursNumber'] ?? "Hours Number";
    metersNumber = json['metersNumber'] ?? "Meters Number";
    filterOrders = json['filterOrders'] ?? "Filter Orders";
    deleteProfile = json['Delete_Profile'] ?? "Delete Profile";
    deleteProfileMessage = json['Are_you_sure_delete_account?'] ??
        "Are You Sure You Want To Delete Your Account?";
    setLocationInMap = json['setLocationInMap?'] ?? "Set location in map";

    albums = json['albums'] ?? "Albums";
    addAlbum = json['addAlbum'] ?? "Add Album";
    editAlbum = json['editAlbum'] ?? "Edit Album";
    addImages = json['addImages'] ?? "Add Images";
    images = json['images'] ?? "Images";
    favorites = json['favorites'] ?? "Favorites";
    myAlbums = json['myAlbums'] ?? "My Albums";
    calculateOffer = json['calculateOffer'] ?? "Calculate Offer";
    appCommission = json['appCommission'] ?? "App Commission";
    tax = json['tax'] ?? "Tax";
    startDate = json['startDate'] ?? "Start Date";
    endDate = json['endDate'] ?? "End Date";
    myProviderOfferServices =
        json['myProviderOfferServices'] ?? "My Provider Offer Services";
    myOfferServices = json['myOfferServices'] ?? "My Offer Services";
    addMore = json['addMore'] ?? "Add More";
    shouldHaveAtLeastOne =
        json['shouldHaveAtLeastOne'] ?? "Should Have At Least One";

    deleteAlbum = json['deleteAlbum'] ?? "Delete Album";
    deleteAlbumMessage = json['deleteAlbumMessage'] ??
        "Are you sure that you want to delete this album?";
    deleteImageMessage = json['deleteImageMessage'] ??
        "Are you sure that you want to delete this image?";
    myFavorites = json['myFavorites'] ?? "My Favorites";
    multi = json['multi'] ?? "Multi";
    waiting = json['waiting'] ?? "Waiting";
    received = json['received'] ?? "Received";
    waitingForApproval = json['waitingForApproval'] ?? "Waiting For Approval";
    deliver = json['deliver'] ?? "Deliver";
    orderEditedSuccessfully =
        json['orderEditedSuccessfully'] ?? "Order Edited Successfully";

    groupTotal = json['groupTotal'] ?? '';
    beAsUser = json['beAsUser'] ?? "Be As User";
    minHours = json['minHours'] ?? "Min hours";
    myCart = json['myCart'] ?? "My Cart";
    needAction = json['needAction'] ?? "Need Action";
    todo = json['todo'] ?? "To Do";
    finishClean = json['finishClean'] ?? "Finish Clean";
    pleaseEnterLocationName =
        json['pleaseEnterLocationName'] ?? "Please Enter Location Name";
    pleaseSelectLocation =
        json['pleaseSelectLocation'] ?? "Please Select Location";
    deleteLocation = json['deleteLocation'] ?? "Delete Location";
    deleteLocationConfirmation = json['deleteLocationConfirmation'] ??
        "Are you sure that you want to delete this location?";
    workLocations = json['workLocations'] ?? "Work Locations";
    noWorkLocationsFound =
        json['noWorkLocationsFound'] ?? "No Work Locations Found";
    viewOnMap = json['viewOnMap'] ?? "View On Map";
    longitude = json['longitude'] ?? "Longitude";
    latitude = json['latitude'] ?? "Latitude";
    enterLocationName = json['enterLocationName'] ?? "Enter Location Name";
    locationName = json['locationName'] ?? "Location Name";
    pleaseEnter = json['pleaseEnter'] ?? "Please enter";

    // Jobs
    jobs = json['jobs'] ?? "Jobs";
    myJobs = json['myJobs'] ?? "My Jobs";
    addJob = json['addJob'] ?? "Add Job";
    editJob = json['editJob'] ?? "Edit Job";
    deleteJob = json['deleteJob'] ?? "Delete Job";
    deleteJobConfirmation = json['deleteJobConfirmation'] ??
        "Are you sure you want to delete this job?";
    jobDetails = json['jobDetails'] ?? "Job Details";
    noJobsFound = json['noJobsFound'] ?? "No Jobs Found";
    selectService = json['selectService'] ?? "Select Service";
    selectLocation = json['selectLocation'] ?? "Select Location";
    notes = json['notes'] ?? "Notes";
    schedule = json['schedule'] ?? "Schedule";
    monday = json['monday'] ?? "Monday";
    tuesday = json['tuesday'] ?? "Tuesday";
    wednesday = json['wednesday'] ?? "Wednesday";
    thursday = json['thursday'] ?? "Thursday";
    friday = json['friday'] ?? "Friday";
    saturday = json['saturday'] ?? "Saturday";
    sunday = json['sunday'] ?? "Sunday";
    startTime = json['startTime'] ?? "Start Time";
    duration = json['duration'] ?? "Duration";
    hours = json['hours'] ?? "Hours";
    pleaseSetSchedule =
        json['pleaseSetSchedule'] ?? "Please set schedule for at least one day";

    // Job Applications
    jobApplications = json['jobApplications'] ?? "Job Applications";
    myJobApplications = json['myJobApplications'] ?? "My Job Applications";
    noJobApplicationsFound =
        json['noJobApplicationsFound'] ?? "No Job Applications Found";
    jobApplicationDetails =
        json['jobApplicationDetails'] ?? "Job Application Details";
    workDays = json['workDays'] ?? "Work Days";
    scheduleHours = json['scheduleHours'] ?? "Schedule Hours";
    cost = json['cost'] ?? "Cost";
    ended = json['ended'] ?? "Ended";
    notStarted = json['notStarted'] ?? "Not Started";
    absent = json['absent'] ?? "Absent";
    approved = json['approved'] ?? "Approved";
    end = json['end'] ?? "End";
    start = json['start'] ?? "Start";
    orderNumber = json['orderNumber'] ?? "Order Number";
    jobApplication = json['jobApplication'] ?? "Job Application";
    order = json['order'] ?? "Order";
    calendarJobs = json['calendarJobs'] ?? "Calendar Jobs";
    jobsFor = json['jobsFor'] ?? "Jobs For";
    noJobsForThisDay = json['noJobsForThisDay'] ?? "No Jobs For This Day";
    confirmed = json['confirmed'] ?? "Confirmed";
    notWithinWorkLocation = json['notWithinWorkLocation'] ??
        "You are not within 100m of the work location";
    taskStartedSuccessfully =
        json['taskStartedSuccessfully'] ?? "Task started successfully";
    taskEndedSuccessfully =
        json['taskEndedSuccessfully'] ?? "Task ended successfully";
    success = json['success'] ?? "Success";
    warning = json['warning'] ?? "Warning";
    areYouSureYouWantToMakeThisAction =
        json['areYouSureYouWantToMakeThisAction'] ??
            "Are you sure you want to make this action?";

    // Calendar Reports
    financialReport = json['financialReport'] ?? "Financial Report";
    filterByPeriod = json['filterByPeriod'] ?? "Filter by Period";
    month = json['month'] ?? "Month";
    year = json['year'] ?? "Year";
    totalTransportation = json['totalTransportation'] ?? "Total Transportation";
    totalAmount = json['totalAmount'] ?? "Total Amount";
    grandTotal = json['grandTotal'] ?? "Grand Total";
    totalTasks = json['totalTasks'] ?? "Total Tasks";
    totalHours = json['totalHours'] ?? "Total Hours";
    noTasksFound = json['noTasksFound'] ?? "No Tasks Found";
    showAllTasks = json['showAllTasks'] ?? "Show All Tasks";
    hideAllTasks = json['hideAllTasks'] ?? "Hide All Tasks";
    executionDate = json['executionDate'] ?? "Execution Date";
    time = json['time'] ?? "Time";
    hours = json['hours'] ?? "Hours";
    amount = json['amount'] ?? "Amount";
    location = json['location'] ?? "Location";
    duration = json['duration'] ?? "Duration";
    minutes = json['minutes'] ?? "Minutes";
    status = json['status'] ?? "Status";
    unpaidAmount = json['unpaidAmount'] ?? "Unpaid Amount";
    payNow = json['payNow'] ?? "Pay Now";
    closedFinancially = json['closedFinancially'] ?? "Closed Financially";
    unClosedFinancially = json['unClosedFinancially'] ?? "UnClosed Financially";
  }
}
