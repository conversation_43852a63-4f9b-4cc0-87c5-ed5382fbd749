import 'package:get_clean/global/models/provider.dart';

class OfferAppointmentsModel {
  int? code;
  bool? success;
  OfferAppointmentData? data;

  OfferAppointmentsModel({this.code, this.success, this.data});

  OfferAppointmentsModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data = json['data'] != null
        ? OfferAppointmentData.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class OfferAppointmentData {
  Offer? offer;
  List<Appointments>? appointments;

  OfferAppointmentData({this.offer, this.appointments});

  OfferAppointmentData.fromJson(Map<String, dynamic> json) {
    offer = json['offer'] != null ? Offer.fromJson(json['offer']) : null;
    if (json['appointments'] != null) {
      appointments = <Appointments>[];
      json['appointments'].forEach((v) {
        appointments!.add(Appointments.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (offer != null) {
      data['offer'] = offer!.toJson();
    }
    if (appointments != null) {
      data['appointments'] = appointments!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Offer {
  int? id;
  String? name;
  String? description;
  Address? address;
  int? price;
  int? duration;
  String? startDate;
  String? endDate;
  bool? isActive;
  String? image;
  Provider? provider;

  Offer(
      {this.id,
      this.name,
      this.description,
      this.address,
      this.price,
      this.duration,
      this.startDate,
      this.endDate,
      this.isActive,
      this.image,
      this.provider});

  Offer.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    address =
        json['address'] != null ? Address.fromJson(json['address']) : null;
    price = json['price'];
    duration = json['duration'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    isActive = json['is_active'];
    image = json['image'];
    provider =
        json['provider'] != null ? Provider.fromJson(json['provider']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    if (address != null) {
      data['address'] = address!.toJson();
    }
    data['price'] = price;
    data['duration'] = duration;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['is_active'] = isActive;
    data['image'] = image;
    if (provider != null) {
      data['provider'] = provider!.toJson();
    }
    return data;
  }
}

class Address {
  int? id;
  String? name;
  Area? area;

  Address({this.id, this.name, this.area});

  Address.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    area = json['area'] != null ? Area.fromJson(json['area']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (area != null) {
      data['area'] = area!.toJson();
    }
    return data;
  }
}

class Area {
  int? id;
  String? name;

  Area({this.id, this.name});

  Area.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class Appointments {
  String? date;
  DateDetails? dateDetails;
  List<String>? bookingTimes;

  Appointments({this.date, this.dateDetails, this.bookingTimes});

  Appointments.fromJson(Map<String, dynamic> json) {
    date = json['date'];
    dateDetails = json['date_details'] != null
        ? DateDetails.fromJson(json['date_details'])
        : null;
    bookingTimes = json['booking_times'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['date'] = date;
    if (dateDetails != null) {
      data['date_details'] = dateDetails!.toJson();
    }
    data['booking_times'] = bookingTimes;
    return data;
  }
}

class DateDetails {
  String? dayNumber;
  String? dayName;

  DateDetails({this.dayNumber, this.dayName});

  DateDetails.fromJson(Map<String, dynamic> json) {
    dayNumber = json['day_number'];
    dayName = json['day_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['day_number'] = dayNumber;
    data['day_name'] = dayName;
    return data;
  }
}
