import 'dart:convert';

class WorkLocationResponse {
  final bool status;
  final String message;
  final List<WorkLocation> data;

  WorkLocationResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory WorkLocationResponse.fromJson(Map<String, dynamic> json) {
    return WorkLocationResponse(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null
          ? List<WorkLocation>.from(
              json['data'].map((x) => WorkLocation.fromJson(x)))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data.map((x) => x.toJson()).toList(),
    };
  }
}

class WorkLocation {
  final int? id;
  final String name;
  final String latitude;
  final String longitude;

  WorkLocation({
    this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
  });

  factory WorkLocation.fromJson(Map<String, dynamic> json) {
    return WorkLocation(
      id: json['id'],
      name: json['name'] ?? '',
      latitude: json['latitude'] ?? '',
      longitude: json['longitude'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}
