import 'package:get/get.dart';

class AllAlbumsModel {
  int? code;
  bool? success;
  List<AlbumsModel>? albums;

  AllAlbumsModel({this.code, this.success, this.albums});

  AllAlbumsModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    if (json['data'] != null) {
      albums = <AlbumsModel>[];
      json['data'].forEach((v) {
        albums!.add(AlbumsModel.fromJson(v));
      });
    }
  }
}

class AlbumsModel {
  int? id;
  String? name;
  String? cover;
  var images = <AlbumImagesModel>[].obs;

  AlbumsModel({
    this.id,
    this.name,
    this.cover,
  });

  AlbumsModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    cover = json['cover'];
    if (json['images'] != null && json['images'].isNotEmpty) {
      images.value = <AlbumImagesModel>[];
      json['images'].forEach((v) {
        images.add(AlbumImagesModel.fromJson(v));
      });
    }
  }
}

class AlbumImagesModel {
  int? id;
  String? photo;
  String? createdAt;
  String? updatedAt;
  int? albumId;

  AlbumImagesModel({
    this.id,
    this.photo,
    this.createdAt,
    this.updatedAt,
    this.albumId,
  });

  AlbumImagesModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    photo = json['photo'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    albumId = json['album_id'];
  }
}
