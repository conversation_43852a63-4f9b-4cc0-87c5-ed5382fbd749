import 'package:get/get.dart';
import 'package:get_clean/global/models/provider.dart';

class AllFavoritesModel {
  int? code;
  bool? success;
  var favoriteProviders = <Provider>[].obs;

  AllFavoritesModel({this.code, this.success, required this.favoriteProviders});

  AllFavoritesModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    if (json['data'] != null) {
      favoriteProviders.value = <Provider>[];
      json['data'].forEach((v) {
        favoriteProviders.add(Provider.fromJson(v));
      });
    }
  }
}
