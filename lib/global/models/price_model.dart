import 'dart:developer';

class PriceModel {
  bool? success;
  int? code;
  Data? data;

  PriceModel({this.success, this.code, this.data});

  PriceModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  num? totalPrice;
  num? discountAmount;
  num? dayPriceWithoutMaterials;
  num? materialsPrice;
  num? dayPrice;
  num? depositPercentage;
  num? daysCount;
  String? deposit;
  List<String>? dates;
  List<String>? durationDate;
  List<String>? durationDay;

  Data(
      {this.totalPrice,
      this.dayPriceWithoutMaterials,
      this.materialsPrice,
      this.dayPrice,
      this.depositPercentage,
      this.discountAmount,
      this.daysCount,
      this.deposit,
      this.dates,
      this.durationDate,
      this.durationDay});

  Data.fromJson(Map<String, dynamic> json) {
    log('asfasfsaf ${json['discount_amount']}');
    totalPrice = json['total_price'];
    discountAmount = json['discount_amount'];
    dayPriceWithoutMaterials = json['day_price_without_materials'];
    materialsPrice = num.tryParse(json['materials_price'] ?? '0');
    dayPrice = json['day_price'];
    depositPercentage =
        num.parse(json['deposit_percentage']?.toString() ?? '0');
    daysCount = num.parse(json['days_count']?.toString() ?? '0');
    deposit = json['deposit']?.toString();
    dates = json['dates'].cast<String>();
    durationDate = json['duration_date'].cast<String>();
    durationDay = json['duration_day'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total_price'] = totalPrice;
    data['day_price_without_materials'] = dayPriceWithoutMaterials;
    data['materials_price'] = materialsPrice;
    data['day_price'] = dayPrice;
    data['deposit_precentage'] = depositPercentage;
    data['days_count'] = daysCount;
    data['deposit'] = deposit;
    data['dates'] = dates;
    data['duration_date'] = durationDate;
    data['duration_day'] = durationDay;
    return data;
  }
}
