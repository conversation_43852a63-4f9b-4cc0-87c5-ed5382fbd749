class ProviderReviews {
  int? code;
  bool? success;
  List<ReviewData>? data;

  ProviderReviews({this.code, this.success, this.data});

  ProviderReviews.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    if (json['data'] != null) {
      data = <ReviewData>[];
      json['data'].forEach((v) {
        data!.add(ReviewData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ReviewData {
  int? id;
  Order? order;
  int? rating;
  String? comment;
  String? createdFrom;

  ReviewData(
      {this.id, this.order, this.rating, this.comment, this.createdFrom});

  ReviewData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    order = json['order'] != null ? Order.fromJson(json['order']) : null;
    rating = json['rating'];
    comment = json['comment'];
    createdFrom = json['created_from'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    if (order != null) {
      data['order'] = order!.toJson();
    }
    data['rating'] = rating;
    data['comment'] = comment;
    data['created_from'] = createdFrom;
    return data;
  }
}

class Order {
  int? id;
  User? user;

  Order({this.id, this.user});

  Order.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    return data;
  }
}

class User {
  int? id;
  String? name;
  String? email;
  String? phone;
  String? gender;
  String? image;

  User({this.id, this.name, this.email, this.phone, this.gender, this.image});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    gender = json['gender'];
    image = json['image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['phone'] = phone;
    data['gender'] = gender;
    data['image'] = image;
    return data;
  }
}
