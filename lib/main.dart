import 'dart:developer';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import 'app/routes/app_pages.dart';
import 'firebase_options.dart';
import 'generated/locales.g.dart';
import 'global/binding/initial_binding.dart';
import 'global/constants/constants.dart';
import 'global/constants/theme.dart';
import 'global/controllers/language_controller.dart';
import 'global/notifications_helper/notifications_helpers.dart';

//! Calendar view months
//! Notifications Read or unread
//! Accept privacy of payment before any payment webview
//!

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      ).whenComplete(() => log('Firebase Initialized'));
    }
  } catch (e) {
    log(e.toString());
  }

  await GetStorage.init();

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // call the initial binding dependencies
  InitialBinding().dependencies();

  await NotificationsHelper.handlingNotification();

  FirebaseMessaging.onBackgroundMessage(
    NotificationsHelper.firebaseMessagingBackgroundHandler,
  );

  runApp(
    const MainPage(),
  );
}

class MainPage extends StatelessWidget {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LanguageController>(
      builder: (controller) {
        log('afasfsf ${GetStorage().read(tokenKey)}');

        return ScreenUtilInit(
          designSize: const Size(390, 844),
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return GetMaterialApp(
              title: "Vish Vish",

              initialRoute: AppPages.initial,
              getPages: AppPages.routes,
              translationsKeys: AppTranslation.translations,

              // locale we use in our app
              locale: Locale(
                controller.selectedLanguage.value.slug ??
                    GetStorage().read(slugKey) ??
                    'en',
              ),

              // app initial binding
              initialBinding: InitialBinding(),

              // set text direction of our app
              textDirection:
                  controller.selectedLanguage.value.direction == 'ltr'
                      ? TextDirection.ltr
                      : TextDirection.rtl,

              debugShowCheckedModeBanner: false,
              theme: customTheme(),
              defaultTransition: Transition.size,
            );
          },
        );
      },
    );
  }
}
